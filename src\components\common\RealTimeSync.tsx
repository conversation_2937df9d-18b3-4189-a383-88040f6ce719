import { useEffect } from 'react';
import { SyncService } from '@/src/lib/services/SyncService';
import { Notification } from '@/src/lib/entities/all';

interface RealTimeSyncProps {
  onPartnerActivity?: (isActive: boolean, lastSync: string | null) => void;
  onNewData?: (changes: unknown) => void;
  onNewNotification?: (notifications: Notification[]) => void;
}

/**
 * This component handles real-time synchronization between partners.
 * It manages user online status, polls for updates, and notifies about partner activity.
 */
export default function RealTimeSync({ 
  onPartnerActivity, 
  onNewData, 
  onNewNotification 
}: RealTimeSyncProps) {


  useEffect(() => {
    // Start the synchronization service
    SyncService.startSync({
      onPartnerStatusChange: (isOnline, lastSync) => {
        if (onPartnerActivity) {
          onPartnerActivity(isOnline, lastSync);
        }
      },
      onDataChange: (changes) => {
        if (onNewData) {
          onNewData(changes);
        }
      },
      onNewNotification: (notifications) => {
        if (onNewNotification) {
          onNewNotification(notifications);
        }
      }
    });

    // Clean up on unmount
    return () => {
      SyncService.stopSync();
    };
  }, [onPartnerActivity, onNewData, onNewNotification]);

  return null; // This is a non-visual component
}
