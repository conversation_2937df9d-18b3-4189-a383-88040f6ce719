"use client";
import React, { useState, useEffect } from "react";
import { User } from "@/src/lib/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { Heart, TrendingUp, MessageSquare, Sparkles, Mail } from "lucide-react";
import { format } from 'date-fns';

export default function Digest() {
  const [weeklyData, setWeeklyData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [user, setUser] = useState(null);

  useEffect(() => {
    loadWeeklyData();
  }, []);

  const loadWeeklyData = async () => {
    setIsLoading(true);
    try {
      const currentUser = await User.me();
      setUser(currentUser);
      const roomCode = sessionStorage.getItem('roomCode');
      if (!roomCode) {
        setIsLoading(false);
        return;
      }

      // Use the DigestService to generate the weekly digest
      const { DigestService } = await import('@/src/lib/services/DigestService');
      const digestData = await DigestService.generateWeeklyDigest(roomCode);
      
      setWeeklyData({
        ...digestData,
        roomCode: currentUser.room_code
      });
    } catch (error) {
      console.error('Error loading weekly data:', error);
    }
    setIsLoading(false);
  };

  const sendEmailDigest = async () => {
    if (!user || !weeklyData) return;
    
    setIsSendingEmail(true);
    try {
      const emailBody = `
        <div style="font-family: Arial, sans-serif; color: #333;">
          <h2 style="color: #8B5CF6;">Your Weekly Relationship Digest</h2>
          <p>Here's your connection summary for ${weeklyData.period}:</p>
          
          <h3 style="color: #EC4899;">📊 Weekly Highlights</h3>
          <ul>
            <li><strong>Total Entries:</strong> ${weeklyData.totalEntries}</li>
            <li><strong>Praise to Issue Ratio:</strong> ${weeklyData.praiseRatio}:1</li>
            <li><strong>Resolution Rate:</strong> ${weeklyData.resolutionRate}%</li>
            <li><strong>New Romance Plans:</strong> ${weeklyData.weeklyPlans}</li>
          </ul>
          
          ${weeklyData.topTag ? `<p><strong>Most Discussed Topic:</strong> ${weeklyData.topTag}</p>` : ''}
          
          <h3 style="color: #10B981;">💝 Love Highlights</h3>
          ${weeklyData.highlights.length > 0 ? weeklyData.highlights.map(entry => `
            <blockquote style="background: #f0fdf4; padding: 10px; margin: 10px 0; border-left: 4px solid #34d399;">
              "${entry.content.substring(0, 100)}${entry.content.length > 100 ? '...' : ''}"
            </blockquote>
          `).join('') : '<p>No praise entries this week. Time to share some love!</p>'}
          
          <p>Keep growing together! 💕</p>
          <p><em>Generated by Between Us for room: ${weeklyData.roomCode}</em></p>
        </div>
      `;

      // Simulate email sending - replace with actual email service
      console.log('Email sent:', emailBody);
      
      setShowEmailDialog(false);
      console.log('Weekly digest sent to your email!');
    } catch (error) {
      console.error('Error sending email:', error);
    }
    setIsSendingEmail(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full animate-pulse mx-auto mb-4"></div>
          <p className="text-orange-600 font-medium">Generating your digest...</p>
        </div>
      </div>
    );
  }

  if (!weeklyData) {
    return (
      <div className="min-h-screen p-6 md:p-8 flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-orange-900 mb-2">No Data for This Week</h3>
          <p className="text-orange-600">Start journaling to see your weekly digest here.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 md:p-8 bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50">
      <div className="max-w-3xl mx-auto space-y-8">
        <div className="text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-orange-900 mb-3">Your Weekly Digest</h1>
          <p className="text-orange-600 mb-4">
            A look back at your week of connection: {weeklyData.period}
          </p>
          <Button
            onClick={() => setShowEmailDialog(true)}
            variant="outline"
            className="bg-white/70 border-orange-300 text-orange-700 hover:bg-orange-50"
          >
            <Mail className="w-4 h-4 mr-2" />
            Email This Digest
          </Button>
        </div>

        <Card className="card-premium border-orange-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-900">
              <TrendingUp className="w-5 h-5 text-orange-500" />
              Weekly Highlights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <p className="text-3xl font-bold text-orange-800">{weeklyData.totalEntries}</p>
                <p className="text-sm text-orange-600">Total Entries</p>
              </div>
              <div>
                <p className="text-3xl font-bold text-green-600">{weeklyData.praiseRatio}:1</p>
                <p className="text-sm text-green-600">Praise Ratio</p>
              </div>
              <div>
                <p className="text-3xl font-bold text-blue-600">{weeklyData.resolutionRate}%</p>
                <p className="text-sm text-blue-600">Resolution Rate</p>
              </div>
              <div>
                <p className="text-3xl font-bold text-purple-600">{weeklyData.weeklyPlans}</p>
                <p className="text-sm text-purple-600">Romance Plans</p>
              </div>
            </div>
            
            {weeklyData.moodData && (
              <div className="mt-6 p-4 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-800 mb-2">Mood Analysis</h4>
                <p className="text-sm text-orange-700">
                  Dominant mood this week: <span className="font-bold">{weeklyData.moodData.dominantMood}</span>
                </p>
                <div className="flex mt-2 h-4 bg-gray-200 rounded-full overflow-hidden">
                  {Object.entries(weeklyData.moodData.moodPercentages || {}).map(([mood, percentage], index) => (
                    <div 
                      key={mood}
                      className="h-full" 
                      className={`mood-${['very-happy', 'happy', 'neutral', 'sad', 'frustrated'][index % 5]}`}
                      style={{ width: `${percentage}%` }}
                      title={`${mood}: ${percentage}%`}
                    />
                  ))}
                </div>
                <div className="flex justify-between mt-1 text-xs text-gray-600">
                  {Object.entries(weeklyData.moodData.moodPercentages || {}).map(([mood, percentage]) => (
                    <span key={mood}>{mood} {percentage}%</span>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {weeklyData.highlights.length > 0 && (
          <Card className="card-premium border-green-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-900">
                <Heart className="w-5 h-5 text-green-500" />
                Love Highlights
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {weeklyData.highlights.map((entry) => (
                <div key={entry.id} className="p-4 bg-green-50 rounded-lg border border-green-100">
                  <p className="text-sm text-green-800 italic">
                    &ldquo;{entry.content.substring(0, 150)}{entry.content.length > 150 ? '...' : ''}&rdquo;
                  </p>
                  <p className="text-xs text-green-600 mt-2 text-right">
                    - {format(new Date(entry.created_date), 'EEEE')}
                  </p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        <Card className="card-premium border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <MessageSquare className="w-5 h-5 text-blue-500" />
              Growth Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {weeklyData.topTag && (
              <p className="text-sm text-blue-800">
                Your most discussed topic this week was <span className="font-semibold">&ldquo;{weeklyData.topTag}&rdquo;</span>.
              </p>
            )}
            
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">💭 Reflection Prompt:</h4>
              <p className="text-blue-800">
                {weeklyData.praiseRatio >= 2 
                  ? "You&apos;re maintaining a beautiful balance of appreciation and growth. How can you continue this positive momentum?"
                  : "Consider adding more expressions of gratitude to balance your growth conversations. What&apos;s one small thing you appreciated this week?"
                }
              </p>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">🎯 Weekly Goals:</h4>
              <ul className="text-purple-800 text-sm space-y-1 list-disc list-inside">
                <li>Continue your open communication pattern.</li>
                <li>{weeklyData.weeklyPlans > 0 ? 'Execute one of your romantic plans.' : 'Create a new romantic surprise.'}</li>
                <li>Schedule dedicated time for a deeper conversation.</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-orange-600">
          Room: <span className="font-semibold">{weeklyData.roomCode}</span> • Generated {format(new Date(), 'PPP')}
        </div>
      </div>

      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Email Weekly Digest</DialogTitle>
            <DialogDescription>
              Send this week&apos;s relationship digest to your email ({user?.email})?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={sendEmailDigest}
              disabled={isSendingEmail}
              className="bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600"
            >
              {isSendingEmail ? (
                <div className="flex items-center gap-2">
                  <div className="spinner" />
                  Sending...
                </div>
              ) : (
                <>
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}