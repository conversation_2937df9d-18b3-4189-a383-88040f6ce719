"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import { Heart, Sparkles, BookOpen, TrendingUp, Shield, Calendar, MessageSquare, ArrowRight } from "lucide-react";

export default function StyleGuide() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50/30 via-pink-50/30 to-rose-50/30 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-pink-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-300/10 to-pink-300/10 rounded-full blur-2xl animate-pulse-romantic"></div>
      </div>

      <div className="container-romantic section-padding relative z-10">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center gap-3 mb-6">
            <div className="w-16 h-16 bg-gradient-romantic rounded-3xl flex items-center justify-center shadow-romantic animate-pulse-romantic">
              <Heart className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-hero font-display gradient-text">Between Us</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            A comprehensive style guide showcasing the romantic, intimate design system that brings couples closer together.
          </p>
        </div>

        {/* Typography Section */}
        <section className="mb-16 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
          <Card className="card-romantic">
            <CardHeader>
              <CardTitle className="font-display text-2xl gradient-text">Typography</CardTitle>
              <CardDescription>Beautiful typography that speaks to the heart</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h1 className="text-hero font-display gradient-text mb-2">Hero Text</h1>
                <p className="text-gray-600">Large, impactful headlines using Playfair Display</p>
              </div>
              <div>
                <h2 className="text-4xl font-display font-bold text-gray-800 mb-2">Display Heading</h2>
                <p className="text-gray-600">Secondary headings with elegant serif styling</p>
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-gray-800 mb-2">Body Heading</h3>
                <p className="text-gray-600">Clean, readable headings using Inter</p>
              </div>
              <div>
                <p className="text-lg text-gray-700 leading-relaxed mb-2">
                  This is body text that's designed for readability and warmth. It uses Inter font family with carefully chosen line heights and spacing.
                </p>
                <p className="text-gray-600">Supporting text in a softer gray tone</p>
              </div>
              <div>
                <span className="gradient-text text-xl font-semibold">Gradient Text</span>
                <span className="ml-4 text-romantic-gradient text-xl font-semibold">Romantic Gradient</span>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Color Palette Section */}
        <section className="mb-16 animate-fade-in-up" style={{animationDelay: '0.4s'}}>
          <Card className="card-romantic">
            <CardHeader>
              <CardTitle className="font-display text-2xl gradient-text">Color Palette</CardTitle>
              <CardDescription>Romantic colors that evoke love and connection</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-romantic rounded-2xl mx-auto mb-3 shadow-romantic"></div>
                  <p className="font-semibold text-gray-800">Primary</p>
                  <p className="text-sm text-gray-600">Purple to Pink</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-romantic-500 rounded-2xl mx-auto mb-3 shadow-romantic"></div>
                  <p className="font-semibold text-gray-800">Romantic</p>
                  <p className="text-sm text-gray-600">#EC4899</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-purple-500 rounded-2xl mx-auto mb-3 shadow-romantic"></div>
                  <p className="font-semibold text-gray-800">Purple</p>
                  <p className="text-sm text-gray-600">#8B5CF6</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-white border-2 border-romantic-200 rounded-2xl mx-auto mb-3 shadow-soft"></div>
                  <p className="font-semibold text-gray-800">Background</p>
                  <p className="text-sm text-gray-600">White/Glass</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Buttons Section */}
        <section className="mb-16 animate-fade-in-up" style={{animationDelay: '0.6s'}}>
          <Card className="card-romantic">
            <CardHeader>
              <CardTitle className="font-display text-2xl gradient-text">Buttons</CardTitle>
              <CardDescription>Interactive elements with delightful hover effects</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800">Primary Buttons</h4>
                  <div className="space-y-3">
                    <Button variant="default" size="lg" className="w-full">
                      Default Button
                      <Heart className="w-4 h-4 ml-2" />
                    </Button>
                    <Button variant="romantic" size="lg" className="w-full">
                      Romantic Button
                      <Sparkles className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800">Secondary Buttons</h4>
                  <div className="space-y-3">
                    <Button variant="outline" size="lg" className="w-full">
                      Outline Button
                    </Button>
                    <Button variant="secondary" size="lg" className="w-full">
                      Secondary Button
                    </Button>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800">Special Buttons</h4>
                  <div className="space-y-3">
                    <Button variant="glass" size="lg" className="w-full">
                      Glass Button
                    </Button>
                    <Button variant="ghost" size="lg" className="w-full">
                      Ghost Button
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Form Elements Section */}
        <section className="mb-16 animate-fade-in-up" style={{animationDelay: '0.8s'}}>
          <Card className="card-romantic">
            <CardHeader>
              <CardTitle className="font-display text-2xl gradient-text">Form Elements</CardTitle>
              <CardDescription>Beautiful, accessible form controls</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Input placeholder="Enter your thoughts..." />
                  <Input placeholder="Email address" type="email" />
                </div>
                <div className="space-y-4">
                  <Textarea placeholder="Share your feelings..." rows={4} />
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Cards Section */}
        <section className="mb-16 animate-fade-in-up" style={{animationDelay: '1s'}}>
          <Card className="card-romantic">
            <CardHeader>
              <CardTitle className="font-display text-2xl gradient-text">Card Variations</CardTitle>
              <CardDescription>Different card styles for various content types</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="card-romantic hover-lift">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-gradient-romantic rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-romantic">
                      <MessageSquare className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="font-display font-bold text-lg text-gray-800 mb-2">Journal Entry</h3>
                    <p className="text-gray-600">Share your daily thoughts and feelings</p>
                  </CardContent>
                </Card>
                
                <Card className="card-glass hover-lift">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-romantic">
                      <TrendingUp className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="font-display font-bold text-lg text-gray-800 mb-2">Growth Tracker</h3>
                    <p className="text-gray-600">Monitor your relationship progress</p>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-romantic-50 to-purple-50 border-romantic-200 hover-lift">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-romantic-500 to-romantic-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-romantic">
                      <Heart className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="font-display font-bold text-lg text-gray-800 mb-2">Love Lab</h3>
                    <p className="text-gray-600">Plan romantic moments together</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Animations Section */}
        <section className="mb-16 animate-fade-in-up" style={{animationDelay: '1.2s'}}>
          <Card className="card-romantic">
            <CardHeader>
              <CardTitle className="font-display text-2xl gradient-text">Animations & Effects</CardTitle>
              <CardDescription>Delightful micro-interactions that bring joy</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
                <div className="space-y-4">
                  <div className="w-20 h-20 bg-gradient-romantic rounded-3xl flex items-center justify-center mx-auto shadow-romantic animate-float">
                    <Heart className="w-10 h-10 text-white" />
                  </div>
                  <p className="font-semibold text-gray-800">Float Animation</p>
                </div>
                <div className="space-y-4">
                  <div className="w-20 h-20 bg-gradient-romantic rounded-3xl flex items-center justify-center mx-auto shadow-romantic animate-pulse-romantic">
                    <Sparkles className="w-10 h-10 text-white" />
                  </div>
                  <p className="font-semibold text-gray-800">Romantic Pulse</p>
                </div>
                <div className="space-y-4">
                  <div className="w-20 h-20 bg-gradient-romantic rounded-3xl flex items-center justify-center mx-auto shadow-romantic hover-lift hover-glow">
                    <BookOpen className="w-10 h-10 text-white" />
                  </div>
                  <p className="font-semibold text-gray-800">Hover Effects</p>
                </div>
                <div className="space-y-4">
                  <div className="spinner-romantic mx-auto"></div>
                  <p className="font-semibold text-gray-800">Loading Spinner</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Call to Action */}
        <div className="text-center animate-fade-in-up" style={{animationDelay: '1.4s'}}>
          <Card className="card-romantic p-12">
            <div className="max-w-2xl mx-auto">
              <h2 className="text-4xl font-display font-bold gradient-text mb-6">
                Ready to Experience the Magic?
              </h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                This beautiful design system creates an intimate, romantic experience that helps couples connect on a deeper level.
              </p>
              <Button variant="romantic" size="xl" className="shadow-romantic hover:shadow-glow group">
                Start Your Journey
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
