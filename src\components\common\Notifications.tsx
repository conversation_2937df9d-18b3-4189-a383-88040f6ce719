import React, { useState } from 'react';
import { Pop<PERSON>, <PERSON>overContent, PopoverTrigger } from "@/src/components/ui/popover";
import { Button } from "@/src/components/ui/button";
import { <PERSON>, Heart, MessageSquare, Vault, Sparkles, <PERSON><PERSON>heck } from "lucide-react";
import { Badge } from "@/src/components/ui/badge";
import { format } from 'date-fns';

const iconMap = {
  new_entry: MessageSquare,
  partner_response: Heart,
  vault_entry: Vault,
  romantic_plan: Sparkles,
  mood_update: Heart,
  room_change_request: MessageSquare,
  join_request: MessageSquare
};

export default function Notifications({ notifications = [], onMarkAsRead, onMarkAllAsRead }: {
  notifications: Notification[],
  onMarkAsRead: (id: string) => Promise<void>,
  onMarkAllAsRead: () => Promise<void>
}) {
  const [isOpen, setIsOpen] = useState(false);

  const handleNotificationClick = async (notification: Notification) => {
    if (notification.type !== 'join_request') {
      await onMarkAsRead(notification.id);
    }

    if (notification.action_url) {
      window.location.href = notification.action_url;
    } else {
      switch (notification.related_type) {
        case 'journal_entry':
          window.location.href = '/journal';
          break;
        case 'romantic_plan':
          window.location.href = '/romance';
          break;
        case 'user_profile':
        case 'join_request':
          window.location.href = '/profile';
          break;
        default:
          window.location.href = '/dashboard';
      }
    }
    setIsOpen(false);
  };

  const handleMarkAllAsRead = async () => {
    if (notifications.length > 0) {
      await onMarkAllAsRead();
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative text-purple-600 hover:bg-purple-100">
          <Bell className="w-5 h-5" />
          {notifications.length > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 w-5 justify-center p-0 text-xs bg-pink-500 text-white border-2 border-white">
              {notifications.length > 9 ? '9+' : notifications.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 max-h-96 overflow-y-auto p-2">
        <div className="space-y-2">
          <div className="flex items-center justify-between p-2">
            <h4 className="font-medium text-purple-900">Notifications</h4>
            {notifications.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="text-xs text-purple-600 hover:text-purple-800"
              >
                <CheckCheck className="w-3 h-3 mr-1" />
                Mark all read
              </Button>
            )}
          </div>

          {notifications.length === 0 ? (
            <div className="text-center py-8 text-purple-500">
              <Bell className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No new notifications</p>
            </div>
          ) : (
            <div className="space-y-1">
              {notifications.map((notification) => {
                const Icon = iconMap[notification.type] || MessageSquare;

                return (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className="flex items-start gap-3 p-3 rounded-lg hover:bg-purple-50 cursor-pointer transition-colors"
                  >
                    <div className="p-2 bg-purple-100 rounded-full flex-shrink-0 mt-1">
                      <Icon className="w-4 h-4 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h5 className="font-medium text-purple-900 text-sm">{notification.title}</h5>
                      <p className="text-sm text-purple-700 line-clamp-2">{notification.message}</p>
                      <p className="text-xs text-purple-500 mt-1">
                        {format(new Date(notification.created_date), 'MMM d, h:mm a')}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}