import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Textarea } from "@/src/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Switch } from "@/src/components/ui/switch";
import { Heart, MessageSquare, AlertTriangle, Sparkles, Save, Send } from "lucide-react";

const entryTypes = [
  { value: 'general', label: 'General Thought', icon: MessageSquare, description: 'Share what\'s on your mind' },
  { value: 'praise', label: 'Praise & Appreciation', icon: Heart, description: 'Celebrate your partner' },
  { value: 'gratitude', label: 'Gratitude', icon: Heart, description: 'Express thankfulness' },
  { value: 'minor_issue', label: 'Minor Issue', icon: Alert<PERSON>riangle, description: 'Small concern to address' },
  { value: 'habitual', label: 'Habitual Pattern', icon: AlertTriangle, description: 'Recurring behavior' },
  { value: 'emotional_hurt', label: 'Emotional Hurt', icon: AlertTriangle, description: 'Something that hurt you' },
  { value: 'needs_discussion', label: 'Needs Discussion', icon: AlertTriangle, description: 'Important topic to talk about' },
  { value: 'romantic', label: 'Romantic', icon: Sparkles, description: 'Love and romance' }
];

const moods = [
  { value: 'very_happy', label: 'Very Happy', emoji: '😊' },
  { value: 'happy', label: 'Happy', emoji: '🙂' },
  { value: 'neutral', label: 'Neutral', emoji: '😐' },
  { value: 'sad', label: 'Sad', emoji: '😔' },
  { value: 'frustrated', label: 'Frustrated', emoji: '😤' },
  { value: 'angry', label: 'Angry', emoji: '😠' }
];

export default function EntryForm({ onSubmit, onCancel, existingDraft, onUpdateDraft }) {
  const [formData, setFormData] = useState({
    entry_type: 'general',
    content: '',
    mood: 'neutral',
    is_draft: false,
    is_secret: false,
    is_vault: false,
    title: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (existingDraft) {
      setFormData({
        entry_type: existingDraft.entry_type || 'general',
        content: existingDraft.content || '',
        mood: existingDraft.mood || 'neutral',
        is_draft: existingDraft.is_draft || false,
        is_secret: existingDraft.is_secret || false,
        is_vault: existingDraft.is_vault || false,
        title: existingDraft.title || ''
      });
    }
  }, [existingDraft]);

  const handleSubmit = async (isDraft = false) => {
    if (!formData.content.trim()) return;

    setIsSubmitting(true);
    try {
      const roomCode = sessionStorage.getItem('roomCode');
      const submitData = {
        ...formData,
        is_draft: isDraft,
        room_code: roomCode,
        created_date: new Date().toISOString()
      };

      if (existingDraft && isDraft) {
        await onUpdateDraft(existingDraft.id, submitData);
      } else {
        await onSubmit(submitData);
      }
    } catch (error) {
      console.error('Error submitting entry:', error);
    }
    setIsSubmitting(false);
  };

  const selectedType = entryTypes.find(type => type.value === formData.entry_type);

  return (
    <Card className="card-premium">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-purple-900">
          {existingDraft ? 'Edit Draft' : 'New Journal Entry'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="entry-type">Entry Type</Label>
            <Select value={formData.entry_type} onValueChange={(value) => setFormData({...formData, entry_type: value})}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {entryTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center gap-2">
                      <type.icon className="w-4 h-4" />
                      <span>{type.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedType && (
              <p className="text-xs text-purple-600">{selectedType.description}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="mood">Current Mood</Label>
            <Select value={formData.mood} onValueChange={(value) => setFormData({...formData, mood: value})}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {moods.map((mood) => (
                  <SelectItem key={mood.value} value={mood.value}>
                    <div className="flex items-center gap-2">
                      <span>{mood.emoji}</span>
                      <span>{mood.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="title">Title (Optional)</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => setFormData({...formData, title: e.target.value})}
            placeholder="Give your entry a title..."
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="content">Your Thoughts</Label>
          <Textarea
            id="content"
            value={formData.content}
            onChange={(e) => setFormData({...formData, content: e.target.value})}
            placeholder="Share what's on your mind..."
            className="min-h-[120px]"
          />
        </div>

        <div className="flex flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="is-secret"
              checked={formData.is_secret}
              onCheckedChange={(checked) => setFormData({...formData, is_secret: checked})}
            />
            <Label htmlFor="is-secret" className="text-sm">Keep private (only you can see)</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is-vault"
              checked={formData.is_vault}
              onCheckedChange={(checked) => setFormData({...formData, is_vault: checked})}
            />
            <Label htmlFor="is-vault" className="text-sm">Add to vault</Label>
          </div>
        </div>

        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            className="flex-1"
          >
            Cancel
          </Button>
          
          <Button
            onClick={() => handleSubmit(true)}
            disabled={!formData.content.trim() || isSubmitting}
            variant="outline"
            className="flex-1"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </Button>

          <Button
            onClick={() => handleSubmit(false)}
            disabled={!formData.content.trim() || isSubmitting}
            className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500"
          >
            <Send className="w-4 h-4 mr-2" />
            {isSubmitting ? 'Sharing...' : 'Share'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}