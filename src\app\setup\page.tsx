"use client";
import React, { useState, useEffect } from "react";
import { User, Notification, Room } from "@/src/lib/entities/all";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar";
import { Heart, Upload, User as UserIcon, Eye, EyeOff, Copy, Check } from "lucide-react";

export default function Setup() {
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [showPin, setShowPin] = useState(false);
  const [copiedRoomCode, setCopiedRoomCode] = useState(false);
  const [isApprovedUser, setIsApprovedUser] = useState(false);
  const [profile, setProfile] = useState({
    email: '',
    full_name: '',
    partner_name: '',
    avatar_url: '',
    room_code: '',
    user_pin: ''
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      const user = await User.me();
      const roomCode = sessionStorage.getItem('roomCode');
      
      const approvedNotifications = await Notification.filter({
        recipient_email: user.email,
        type: 'join_approved',
        room_code: roomCode
      });
      
      const isApproved = approvedNotifications.length > 0;
      setIsApprovedUser(isApproved);

      const usersInRoom = await User.filter({ room_code: roomCode });
      const existingUser = usersInRoom.find(u => u.email === user.email);
      
      if (existingUser && existingUser.full_name && existingUser.user_pin && !isApproved) {
        sessionStorage.setItem('userPin', existingUser.user_pin);
        window.location.href = '/dashboard';
        return;
      }
      
      setProfile({
        email: user.email || '',
        full_name: '',
        partner_name: '',
        avatar_url: '',
        room_code: roomCode || '',
        user_pin: ''
      });
    } catch {
      console.error('Error loading user data');
    }
  };

  const handleAvatarUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploadingAvatar(true);
    try {
      // Simulate upload - replace with actual upload logic
      const fileUrl = URL.createObjectURL(file);
      setProfile(prev => ({ ...prev, avatar_url: fileUrl }));
      console.log('Avatar uploaded successfully!');
    } catch {
      console.error('Error uploading avatar');
    }
    setUploadingAvatar(false);
  };

  const copyRoomCode = async () => {
    try {
      await navigator.clipboard.writeText(profile.room_code);
      setCopiedRoomCode(true);
      console.log('Room code copied to clipboard!');
      setTimeout(() => setCopiedRoomCode(false), 2000);
    } catch {
      console.error('Failed to copy room code.');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!profile.email.trim() || !profile.full_name.trim() || !profile.room_code.trim() || profile.user_pin.length !== 8) {
      console.error("Please fill in all required fields.");
      return;
    }

    setIsLoading(true);
    try {
      await User.updateMyUserData({
        full_name: profile.full_name,
        partner_name: profile.partner_name,
        avatar_url: profile.avatar_url,
        room_code: profile.room_code,
        user_pin: profile.user_pin,
        is_online: true,
        last_sync: new Date().toISOString(),
        notification_preferences: {
          email_digest: true,
          partner_notifications: true,
          push_notifications: true
        }
      });
      
      const existingRooms = await Room.filter({ room_code: profile.room_code });
      if (existingRooms.length === 0) {
        await Room.create({
          room_code: profile.room_code,
          creator_email: profile.email,
        });
      }

      if (isApprovedUser) {
        const approvedNotifications = await Notification.filter({
          recipient_email: profile.email,
          type: 'join_approved',
          room_code: profile.room_code
        });
        
        for (const notification of approvedNotifications) {
          await Notification.update(notification.id, { is_read: true });
        }
      }

      sessionStorage.setItem('roomCode', profile.room_code);
      sessionStorage.setItem('userPin', profile.user_pin);

      console.log('Profile setup completed successfully!');
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('Error saving profile:', error);
    }
    setIsLoading(false);
  };

  const getInitials = (name) => {
    if (!name) return '';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-100 p-4 flex items-center justify-center">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-block p-4 bg-white rounded-full shadow-lg mb-4">
            <Heart className="w-12 h-12 text-pink-500" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-500 text-transparent bg-clip-text mb-2">
            Complete Your Profile
          </h1>
          <p className="text-purple-700">
            Let&apos;s set up your personal space
          </p>
        </div>

        <Card className="bg-white/70 backdrop-blur-lg border-0 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl text-purple-900 text-center">Profile Setup</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="text-center">
                <div className="relative inline-block">
                  <Avatar className="w-24 h-24 mx-auto">
                    <AvatarImage src={profile.avatar_url} />
                    <AvatarFallback className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xl">
                      {profile.full_name ? getInitials(profile.full_name) : <UserIcon className="w-10 h-10" />}
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    type="button"
                    size="icon"
                    className="absolute -bottom-2 -right-2 rounded-full bg-purple-500 hover:bg-purple-600"
                    onClick={() => document.getElementById('avatar-upload').click()}
                    disabled={uploadingAvatar}
                  >
                    {uploadingAvatar ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    ) : (
                      <Upload className="w-4 h-4" />
                    )}
                  </Button>
                  <input
                    id="avatar-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                  />
                </div>
                <p className="text-xs text-purple-600 mt-2">Click to upload your photo</p>
              </div>

              <div>
                <Label htmlFor="email" className="text-purple-800 font-medium">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={profile.email}
                  className="mt-1 h-12 bg-gray-100"
                  disabled
                />
                <p className="text-xs text-purple-600 mt-1">Your authenticated email address</p>
              </div>

              <div>
                <Label htmlFor="full_name" className="text-purple-800 font-medium">Your Full Name *</Label>
                <Input
                  id="full_name"
                  value={profile.full_name}
                  onChange={(e) => setProfile(prev => ({ ...prev, full_name: e.target.value }))}
                  placeholder="Enter your full name"
                  className="mt-1 h-12"
                  required
                />
              </div>

              <div>
                <Label htmlFor="partner_name" className="text-purple-800 font-medium">Partner&apos;s Name</Label>
                <Input
                  id="partner_name"
                  value={profile.partner_name}
                  onChange={(e) => setProfile(prev => ({ ...prev, partner_name: e.target.value }))}
                  placeholder="Your partner&apos;s name (optional)"
                  className="mt-1 h-12"
                />
              </div>

              <div>
                <Label htmlFor="room_code" className="text-purple-800 font-medium">Room Code *</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    id="room_code"
                    value={profile.room_code}
                    onChange={(e) => setProfile(prev => ({ ...prev, room_code: e.target.value }))}
                    placeholder="Your shared room code"
                    className="h-12 flex-1"
                    required
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={copyRoomCode}
                    className="h-12 w-12"
                    disabled={!profile.room_code}
                  >
                    {copiedRoomCode ? <Check className="w-4 h-4 text-green-600" /> : <Copy className="w-4 h-4" />}
                  </Button>
                </div>
                <p className="text-xs text-purple-600 mt-1">
                  Share this code with your partner
                </p>
              </div>

              <div>
                <Label htmlFor="user_pin" className="text-purple-800 font-medium">Choose Your Personal Pin *</Label>
                <div className="relative">
                  <Input
                    id="user_pin"
                    type={showPin ? "text" : "password"}
                    value={profile.user_pin}
                    onChange={(e) => setProfile(prev => ({ ...prev, user_pin: e.target.value.slice(0, 8) }))}
                    placeholder="8-digit pin"
                    maxLength={8}
                    className="mt-1 h-12 pr-12"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPin(!showPin)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-500 hover:text-purple-700"
                  >
                    {showPin ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                <p className="text-xs text-purple-600 mt-1">Choose your unique 8-digit pin (different from your partner&apos;s)</p>
              </div>

              <Button
                type="submit"
                disabled={isLoading || !profile.email.trim() || !profile.full_name.trim() || !profile.room_code.trim() || profile.user_pin.length !== 8}
                className="w-full h-12 text-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Setting up...
                  </div>
                ) : (
                  "Complete Setup"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}