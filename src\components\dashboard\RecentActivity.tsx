import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { format } from 'date-fns';
import { MessageSquare, Heart, AlertCircle, Sparkles } from "lucide-react";

const entryTypeIcons = {
  praise: Heart,
  gratitude: Heart,
  minor_issue: Alert<PERSON>ircle,
  habitual: Alert<PERSON>ircle,
  emotional_hurt: AlertCircle,
  needs_discussion: Alert<PERSON>ircle,
  general: MessageSquare,
  romantic: Sparkles
};

const entryTypeColors = {
  praise: "bg-green-100 text-green-800",
  gratitude: "bg-pink-100 text-pink-800",
  minor_issue: "bg-yellow-100 text-yellow-800",
  habitual: "bg-orange-100 text-orange-800",
  emotional_hurt: "bg-red-100 text-red-800",
  needs_discussion: "bg-blue-100 text-blue-800",
  general: "bg-gray-100 text-gray-800",
  romantic: "bg-purple-100 text-purple-800"
};

export default function RecentActivity({ entries, currentUser }) {
  const recentEntries = entries
    .sort((a, b) => new Date(b.created_date) - new Date(a.created_date))
    .slice(0, 5);

  return (
    <Card className="card-premium">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-purple-900">Recent Activity</CardTitle>
        <p className="text-sm text-purple-600">Latest journal entries from both of you</p>
      </CardHeader>
      <CardContent>
        {recentEntries.length === 0 ? (
          <div className="text-center py-8 text-purple-500">
            <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">No entries yet. Start journaling together!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {recentEntries.map((entry, index) => {
              const Icon = entryTypeIcons[entry.entry_type] || MessageSquare;
              const isCurrentUser = entry.author_email === currentUser?.email;
              
              return (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg hover:bg-purple-50 transition-colors">
                  <div className="p-2 bg-purple-100 rounded-full flex-shrink-0">
                    <Icon className="w-4 h-4 text-purple-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-purple-900 text-sm">
                        {isCurrentUser ? 'You' : 'Your Partner'}
                      </span>
                      <Badge className={`text-xs ${entryTypeColors[entry.entry_type] || entryTypeColors.general}`}>
                        {entry.entry_type?.replace('_', ' ')}
                      </Badge>
                    </div>
                    <p className="text-sm text-purple-700 line-clamp-2">
                      {entry.content?.substring(0, 100)}...
                    </p>
                    <p className="text-xs text-purple-500 mt-1">
                      {format(new Date(entry.created_date), 'MMM d, h:mm a')}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}