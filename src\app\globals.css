@tailwind base;
@tailwind components;
@tailwind utilities;

html, body {
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: inherit;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:root, [data-theme="default"] {
  --primary-gradient: linear-gradient(135deg, #E8D5FF 0%, #FFE4E6 100%);
  --secondary-gradient: linear-gradient(135deg, #F3E8FF 0%, #FFF1F2 100%);
  --accent-gradient: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --border-soft: #E5E7EB;
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.08);
}

[data-theme="ocean"] {
  --primary-gradient: linear-gradient(135deg, #A5F3FC 0%, #BFDBFE 100%);
  --secondary-gradient: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 100%);
  --accent-gradient: linear-gradient(135deg, #38BDF8 0%, #3B82F6 100%);
}

[data-theme="sunrise"] {
  --primary-gradient: linear-gradient(135deg, #FEF9C3 0%, #FED7AA 100%);
  --secondary-gradient: linear-gradient(135deg, #FFFBEB 0%, #FFEDD5 100%);
  --accent-gradient: linear-gradient(135deg, #FBBF24 0%, #F97316 100%);
}

[data-theme="forest"] {
  --primary-gradient: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%);
  --secondary-gradient: linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 100%);
  --accent-gradient: linear-gradient(135deg, #34D399 0%, #10B981 100%);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: var(--secondary-gradient);
}

.gradient-text {
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-premium {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-medium);
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Progress bar styles */
.progress-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #8b5cf6, #ec4899);
  transition: width 0.3s ease;
}

/* Mood color variables */
.mood-very-happy { background-color: #FCD34D; }
.mood-happy { background-color: #34D399; }
.mood-neutral { background-color: #60A5FA; }
.mood-sad { background-color: #F87171; }
.mood-frustrated { background-color: #A78BFA; }
.mood-angry { background-color: #EF4444; }

/* Loading spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Email template styles */
.email-template {
  font-family: Arial, sans-serif;
  color: #333;
}

.email-header {
  color: #8B5CF6;
}

.email-section {
  color: #EC4899;
}

.email-highlight {
  background: #f0fdf4;
  padding: 10px;
  margin: 10px 0;
  border-left: 4px solid #34d399;
}

.email-footer {
  color: #10B981;
}