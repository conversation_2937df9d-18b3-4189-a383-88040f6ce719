@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

html, body {
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

*, *::before, *::after {
  box-sizing: inherit;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:root, [data-theme="default"] {
  /* Enhanced Primary Gradients */
  --primary-gradient: linear-gradient(135deg, #E8D5FF 0%, #F8E8FF 50%, #FFE4E6 100%);
  --secondary-gradient: linear-gradient(135deg, #F3E8FF 0%, #FEFBFF 50%, #FFF1F2 100%);
  --accent-gradient: linear-gradient(135deg, #8B5CF6 0%, #A855F7 25%, #C084FC 50%, #EC4899 75%, #F472B6 100%);
  --hero-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #8B5CF6 50%, #EC4899 75%, #f093fb 100%);

  /* Sophisticated Background Gradients */
  --bg-primary: linear-gradient(135deg, #fdfcff 0%, #f8f4ff 25%, #fef7f7 50%, #fff1f2 75%, #fef2f2 100%);
  --bg-secondary: linear-gradient(135deg, #ffffff 0%, #fefbff 50%, #fff8f8 100%);
  --bg-card: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  --bg-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);

  /* Enhanced Text Colors */
  --text-primary: #1a1a2e;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --text-accent: #8B5CF6;
  --text-romantic: #EC4899;

  /* Sophisticated Borders */
  --border-soft: rgba(139, 92, 246, 0.1);
  --border-medium: rgba(139, 92, 246, 0.2);
  --border-accent: rgba(236, 72, 153, 0.3);
  --border-glass: rgba(255, 255, 255, 0.2);

  /* Enhanced Shadows */
  --shadow-soft: 0 4px 6px -1px rgba(139, 92, 246, 0.05), 0 2px 4px -1px rgba(236, 72, 153, 0.03);
  --shadow-medium: 0 10px 15px -3px rgba(139, 92, 246, 0.08), 0 4px 6px -2px rgba(236, 72, 153, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(139, 92, 246, 0.1), 0 10px 10px -5px rgba(236, 72, 153, 0.04);
  --shadow-romantic: 0 8px 32px rgba(236, 72, 153, 0.15), 0 4px 16px rgba(139, 92, 246, 0.1);
  --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.3), 0 0 40px rgba(236, 72, 153, 0.2);

  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);
}

[data-theme="ocean"] {
  --primary-gradient: linear-gradient(135deg, #A5F3FC 0%, #BFDBFE 50%, #E0F2FE 100%);
  --secondary-gradient: linear-gradient(135deg, #F0F9FF 0%, #F0FDFF 50%, #E0F2FE 100%);
  --accent-gradient: linear-gradient(135deg, #0EA5E9 0%, #38BDF8 25%, #0284C7 50%, #3B82F6 75%, #1D4ED8 100%);
  --hero-gradient: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 25%, #3b82f6 50%, #1d4ed8 75%, #1e40af 100%);
  --bg-primary: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f0fdff 50%, #ecfeff 75%, #cffafe 100%);
  --text-accent: #0EA5E9;
  --text-romantic: #3B82F6;
  --border-soft: rgba(14, 165, 233, 0.1);
  --border-medium: rgba(14, 165, 233, 0.2);
  --border-accent: rgba(59, 130, 246, 0.3);
}

[data-theme="sunrise"] {
  --primary-gradient: linear-gradient(135deg, #FEF9C3 0%, #FED7AA 50%, #FFEDD5 100%);
  --secondary-gradient: linear-gradient(135deg, #FFFBEB 0%, #FEF3C7 50%, #FFEDD5 100%);
  --accent-gradient: linear-gradient(135deg, #F59E0B 0%, #FBBF24 25%, #F97316 50%, #EA580C 75%, #DC2626 100%);
  --hero-gradient: linear-gradient(135deg, #f59e0b 0%, #fbbf24 25%, #f97316 50%, #ea580c 75%, #dc2626 100%);
  --bg-primary: linear-gradient(135deg, #fffbeb 0%, #fef3c7 25%, #ffedd5 50%, #fed7aa 75%, #fdba74 100%);
  --text-accent: #F59E0B;
  --text-romantic: #F97316;
  --border-soft: rgba(245, 158, 11, 0.1);
  --border-medium: rgba(245, 158, 11, 0.2);
  --border-accent: rgba(249, 115, 22, 0.3);
}

[data-theme="forest"] {
  --primary-gradient: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 50%, #6EE7B7 100%);
  --secondary-gradient: linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 50%, #BBF7D0 100%);
  --accent-gradient: linear-gradient(135deg, #059669 0%, #10B981 25%, #34D399 50%, #6EE7B7 75%, #A7F3D0 100%);
  --hero-gradient: linear-gradient(135deg, #059669 0%, #10b981 25%, #34d399 50%, #6ee7b7 75%, #a7f3d0 100%);
  --bg-primary: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 25%, #bbf7d0 50%, #a7f3d0 75%, #6ee7b7 100%);
  --text-accent: #059669;
  --text-romantic: #10B981;
  --border-soft: rgba(5, 150, 105, 0.1);
  --border-medium: rgba(5, 150, 105, 0.2);
  --border-accent: rgba(16, 185, 129, 0.3);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
.font-display {
  font-family: 'Playfair Display', Georgia, serif;
}

.gradient-text {
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.gradient-text-romantic {
  background: linear-gradient(135deg, #EC4899 0%, #F472B6 50%, #FB7185 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Enhanced Card Styles */
.card-premium {
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass);
  box-shadow: var(--shadow-romantic);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-premium:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
  border-color: var(--border-accent);
}

.card-glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.card-romantic {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 244, 255, 0.8) 100%);
  border: 1px solid rgba(236, 72, 153, 0.1);
  box-shadow: 0 8px 32px rgba(236, 72, 153, 0.1), 0 4px 16px rgba(139, 92, 246, 0.05);
}

/* Enhanced Scrollbar Styles */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-romantic {
  scrollbar-width: thin;
  scrollbar-color: rgba(236, 72, 153, 0.3) transparent;
}

.scrollbar-romantic::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-romantic::-webkit-scrollbar-track {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 3px;
}

.scrollbar-romantic::-webkit-scrollbar-thumb {
  background: var(--accent-gradient);
  border-radius: 3px;
}

.scrollbar-romantic::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #A855F7 0%, #F472B6 100%);
}

/* Enhanced Progress Bar Styles */
.progress-bar {
  height: 10px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
  border-radius: 9999px;
  overflow: hidden;
  position: relative;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

.progress-fill {
  height: 100%;
  background: var(--accent-gradient);
  border-radius: 9999px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: progress-shine 2s infinite;
}

/* Enhanced Mood Color Variables */
.mood-very-happy {
  background: linear-gradient(135deg, #FCD34D 0%, #F59E0B 100%);
  box-shadow: 0 4px 12px rgba(252, 211, 77, 0.4);
}
.mood-happy {
  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);
  box-shadow: 0 4px 12px rgba(52, 211, 153, 0.4);
}
.mood-neutral {
  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
}
.mood-sad {
  background: linear-gradient(135deg, #F87171 0%, #EF4444 100%);
  box-shadow: 0 4px 12px rgba(248, 113, 113, 0.4);
}
.mood-frustrated {
  background: linear-gradient(135deg, #A78BFA 0%, #8B5CF6 100%);
  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.4);
}
.mood-angry {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Enhanced Loading Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(139, 92, 246, 0.2);
  border-top: 3px solid #8B5CF6;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

.spinner-romantic {
  width: 24px;
  height: 24px;
  border: 3px solid transparent;
  border-top: 3px solid #EC4899;
  border-right: 3px solid #8B5CF6;
  border-radius: 50%;
  animation: spin-romantic 1.5s linear infinite;
}

/* Enhanced Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spin-romantic {
  0% {
    transform: rotate(0deg);
    border-top-color: #EC4899;
    border-right-color: #8B5CF6;
  }
  50% {
    transform: rotate(180deg);
    border-top-color: #8B5CF6;
    border-right-color: #EC4899;
  }
  100% {
    transform: rotate(360deg);
    border-top-color: #EC4899;
    border-right-color: #8B5CF6;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-romantic {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(236, 72, 153, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(236, 72, 153, 0);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-romantic {
  animation: pulse-romantic 2s infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.4s ease-out;
}

/* Button Enhancements */
.btn-romantic {
  background: var(--accent-gradient);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-romantic);
  position: relative;
  overflow: hidden;
}

.btn-romantic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-romantic:hover::before {
  left: 100%;
}

.btn-romantic:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.btn-romantic:active {
  transform: translateY(0);
}

.btn-glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.4);
  border-color: var(--border-accent);
  transform: translateY(-1px);
}

/* Input Enhancements */
.input-romantic {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid var(--border-soft);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.input-romantic:focus {
  outline: none;
  border-color: var(--text-accent);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.input-romantic::placeholder {
  color: var(--text-muted);
}

/* Text Enhancements */
.text-romantic-gradient {
  background: linear-gradient(135deg, #EC4899 0%, #8B5CF6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-hero {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-display {
  font-family: 'Playfair Display', Georgia, serif;
  font-weight: 600;
}

/* Layout Enhancements */
.container-romantic {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-padding {
  padding: 4rem 0;
}

.section-padding-lg {
  padding: 6rem 0;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  .section-padding {
    padding: 2rem 0;
  }

  .section-padding-lg {
    padding: 3rem 0;
  }

  .text-hero {
    font-size: clamp(1.5rem, 8vw, 2.5rem);
  }
}

/* Dark Mode Support (for future implementation) */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    --bg-secondary: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    --bg-card: linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(22, 33, 62, 0.7) 100%);
  }
}

/* Email template styles */
.email-template {
  font-family: Arial, sans-serif;
  color: #333;
}

.email-header {
  color: #8B5CF6;
}

.email-section {
  color: #EC4899;
}

.email-highlight {
  background: #f0fdf4;
  padding: 10px;
  margin: 10px 0;
  border-left: 4px solid #34d399;
}

.email-footer {
  color: #10B981;
}