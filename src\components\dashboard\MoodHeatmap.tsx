import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/src/components/ui/card";
import { format, subDays } from 'date-fns';

const moodColors = {
  very_happy: 'bg-green-400',
  happy: 'bg-green-300',
  neutral: 'bg-gray-300',
  sad: 'bg-blue-300',
  frustrated: 'bg-yellow-400',
  angry: 'bg-red-400'
};

const moodEmojis = {
  very_happy: '😊',
  happy: '🙂',
  neutral: '😐',
  sad: '😔',
  frustrated: '😤',
  angry: '😠'
};

export default function MoodHeatmap({ entries }) {
  const last30Days = Array.from({ length: 30 }, (_, i) => subDays(new Date(), 29 - i));
  
  const getMoodForDay = (date) => {
    const dayEntries = entries.filter(entry => 
      format(new Date(entry.created_date), 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
    );
    
    if (dayEntries.length === 0) return null;
    
    return dayEntries[dayEntries.length - 1].mood;
  };

  return (
    <Card className="card-premium">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-purple-900">Mood Journey</CardTitle>
        <p className="text-sm text-purple-600">Your emotional patterns over the last 30 days</p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-10 gap-1">
          {last30Days.map((date, index) => {
            const mood = getMoodForDay(date);
            return (
              <div
                key={index}
                className={`w-6 h-6 rounded-lg flex items-center justify-center text-xs transition-all duration-200 hover:scale-110 ${
                  mood ? moodColors[mood] : 'bg-gray-100'
                }`}
                title={`${format(date, 'MMM d')}: ${mood ? mood.replace('_', ' ') : 'No entry'}`}
              >
                {mood && moodEmojis[mood]}
              </div>
            );
          })}
        </div>
        <div className="flex items-center justify-between mt-4 text-xs text-purple-600">
          <span>30 days ago</span>
          <span>Today</span>
        </div>
      </CardContent>
    </Card>
  );
}