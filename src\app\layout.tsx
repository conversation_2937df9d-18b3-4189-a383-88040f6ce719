"use client";
import React, { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Heart, BookOpen, Vault, Sparkles, TrendingUp, User as UserIcon, LogOut, FileText } from "lucide-react";
import { Toaster } from "sonner";
import "./globals.css";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  SidebarProvider,
  SidebarTrigger,
} from "@/src/components/ui/sidebar";
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar";
import Notifications from "@/src/components/common/Notifications";
import ThemeSwitcher from "@/src/components/common/ThemeSwitcher";
import RealTimeSync from "@/src/components/common/RealTimeSync";
import { NotificationService, UserService } from "@/src/lib/entities/all";

const navigationItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Sparkles,
  },
  {
    title: "Live Journal",
    url: "/journal",
    icon: BookOpen,
  },
  {
    title: "The Vault",
    url: "/vault",
    icon: Vault,
  },
  {
    title: "Growth Tracker",
    url: "/growth",
    icon: TrendingUp,
  },
  {
    title: "Love Lab",
    url: "/romance",
    icon: Heart,
  },
  {
    title: "Weekly Digest",
    url: "/digest",
    icon: FileText,
  }
];

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [theme, setTheme] = useState('default');
  const [notifications, setNotifications] = useState([]);
  const [partnerStatus, setPartnerStatus] = useState({ online: false, lastSync: null });
  const [user, setUser] = useState(null);

  const currentPageName = pathname === '/' ? 'Home' : 
    pathname === '/landing' ? 'Landing' :
    pathname === '/setup' ? 'Setup' :
    pathname.split('/')[1]?.charAt(0).toUpperCase() + pathname.split('/')[1]?.slice(1) || 'Dashboard';

  useEffect(() => {
    const roomCode = sessionStorage.getItem('roomCode');
    const userPin = sessionStorage.getItem('userPin');
    if ((!roomCode || !userPin) && !['Landing', 'Home', 'Setup'].includes(currentPageName)) {
      router.push('/landing');
    } else if (roomCode) {
      loadUserAndNotifications();
    }
  }, [pathname, router, currentPageName]);

  const loadUserAndNotifications = async () => {
    try {
      const currentUser = await UserService.me();
      setUser(currentUser);
      
      const roomCode = sessionStorage.getItem('roomCode');
      if (roomCode) {
        try {
          const userNotifications = await NotificationService.filter({
            recipient_email: currentUser.email,
            room_code: roomCode,
            is_read: false
          }, '-created_date');
          setNotifications(userNotifications);
        } catch (notificationError) {
          console.warn('Could not load notifications:', notificationError);
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const handleLogout = async () => {
    try {
      if (user) {
        await UserService.updateMyUserData({
          is_online: false,
          last_sync: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error updating user status on logout:', error);
    }
    
    sessionStorage.removeItem('roomCode');
    sessionStorage.removeItem('userPin');
    router.push('/home');
  };

  const handlePartnerActivity = useCallback((isOnline, lastActivity) => {
    setPartnerStatus({ online: isOnline, lastSync: lastActivity });
  }, []);

  const markNotificationAsRead = async (notificationId) => {
    try {
      await NotificationService.update(notificationId, { is_read: true });
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllNotificationsAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.is_read);
      if (unreadNotifications.length === 0) return;

      const updatePromises = unreadNotifications.map(n => 
        NotificationService.update(n.id, { is_read: true })
      );
      await Promise.all(updatePromises);
      setNotifications([]);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const getInitials = (name) => {
    if (!name) return '';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  // Do not render layout for certain pages
  if (['Landing', 'Home', 'Setup'].includes(currentPageName)) {
    return (
      <html lang="en">
        <body>
          {children}
          <Toaster position="top-right" />
        </body>
      </html>
    );
  }

  return (
    <html lang="en">
      <body>
        <SidebarProvider>
          <Toaster position="top-right" />
          <RealTimeSync 
            onPartnerActivity={handlePartnerActivity}
            onNewData={() => {
              // Refresh data when changes are detected
              loadUserAndNotifications();
            }}
            onNewNotification={(newNotifications) => {
              // Update notifications when new ones arrive
              setNotifications(prev => [...newNotifications, ...prev]);
            }}
          />

          <div className="min-h-screen flex w-full" data-theme={theme}>
            <Sidebar className="border-r border-purple-100 bg-gradient-to-b from-purple-50 to-pink-50 transition-colors duration-300 hidden md:flex md:flex-col">
              <SidebarHeader className="border-b border-purple-100 p-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="font-bold text-xl gradient-text">Between Us</h2>
                    <p className="text-xs text-purple-600">Emotional Connection</p>
                  </div>
                </div>
              </SidebarHeader>
              
              <SidebarContent className="p-3 flex-1 overflow-y-auto">
                <SidebarGroup>
                  <SidebarGroupLabel className="text-xs font-semibold text-purple-700 uppercase tracking-wider px-3 py-3">
                    Navigation
                  </SidebarGroupLabel>
                  <SidebarGroupContent>
                    <SidebarMenu className="space-y-1">
                      {navigationItems.map((item) => (
                        <SidebarMenuItem key={item.title}>
                          <SidebarMenuButton 
                            asChild 
                            className={`hover:bg-purple-100 hover:text-purple-800 transition-all duration-300 rounded-xl mb-1 ${
                              pathname === item.url ? 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 shadow-sm' : ''
                            }`}
                          >
                            <Link href={item.url} className="flex items-center gap-3 px-4 py-3">
                              <item.icon className="w-5 h-5" />
                              <span className="font-medium">{item.title}</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                </SidebarGroup>

                <SidebarGroup>
                  <SidebarGroupLabel className="text-xs font-semibold text-purple-700 uppercase tracking-wider px-3 py-3">
                    Connection Status
                  </SidebarGroupLabel>
                  <SidebarGroupContent>
                    <div className="px-4 py-3 space-y-3">
                      <div className="flex items-center gap-3 text-sm">
                        <div className={`w-3 h-3 rounded-full ${partnerStatus.online ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                        <span className="font-medium text-gray-600">
                          Partner is {partnerStatus.online ? 'Online' : 'Offline'}
                        </span>
                      </div>
                      <div className="text-xs text-purple-600">
                        {partnerStatus.lastSync 
                          ? `Last seen: ${new Date(partnerStatus.lastSync).toLocaleTimeString()}`
                          : 'Connection status updated in real-time'
                        }
                      </div>
                    </div>
                  </SidebarGroupContent>
                </SidebarGroup>
              </SidebarContent>

              <SidebarFooter className="border-t border-purple-100 p-4 space-y-2">
                <Link 
                  href="/profile" 
                  className="flex items-center gap-3 p-3 rounded-xl hover:bg-purple-100 transition-colors"
                >
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={user?.avatar_url} />
                    <AvatarFallback className="bg-gradient-to-r from-purple-400 to-pink-400 text-white text-xs">
                      {user?.full_name ? getInitials(user.full_name) : <UserIcon className="w-4 h-4" />}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-purple-900 text-sm truncate">
                      {user?.full_name || 'Your Profile'}
                    </p>
                    <p className="text-xs text-purple-600 truncate">Settings & Preferences</p>
                  </div>
                </Link>
                <div className="flex items-center justify-between">
                  <ThemeSwitcher setTheme={setTheme} />
                  <button 
                    className="flex items-center gap-2 text-purple-600 hover:bg-purple-100 px-3 py-2 rounded-lg transition-colors text-sm"
                    onClick={handleLogout}
                  >
                    <LogOut className="w-4 h-4" />
                    Logout
                  </button>
                </div>
              </SidebarFooter>
            </Sidebar>

            <main className="flex-1 flex flex-col bg-secondary-gradient transition-colors duration-300" style={{ background: 'var(--secondary-gradient)' }}>
              <header className="bg-white/70 backdrop-blur-sm border-b border-border-soft px-6 py-3 sticky top-0 z-10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <SidebarTrigger className="hover:bg-purple-100 p-2 rounded-lg transition-colors duration-200 md:hidden" />
                    <h1 className="text-xl font-bold gradient-text hidden md:block">{currentPageName}</h1>
                  </div>
                  <div className="flex items-center gap-2">
                    <Notifications 
                      notifications={notifications} 
                      onMarkAsRead={markNotificationAsRead}
                      onMarkAllAsRead={markAllNotificationsAsRead}
                    />
                  </div>
                </div>
              </header>

              <div className="flex-1 overflow-auto p-4 sm:p-6 md:p-8">
                {children}
              </div>
            </main>
          </div>
        </SidebarProvider>
      </body>
    </html>
  );
}