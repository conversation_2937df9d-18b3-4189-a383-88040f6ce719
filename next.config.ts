import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* Enable experimental features for better performance */
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-avatar', '@radix-ui/react-dialog'],
    turbo: {
      root: '.',
    },
  },
  
  /* Optimize compilation and performance */
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  /* Enable modern image optimization */
  images: {
    formats: ['image/webp', 'image/avif'],
  },
  
  /* Optimize build output */
  output: 'standalone',
};

export default nextConfig;
