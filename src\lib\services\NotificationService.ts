import { NotificationService as BaseNotificationService, User } from '@/src/lib/entities/all';

/**
 * Enhanced notification service with additional functionality
 */
export class NotificationService {
  /**
   * Create a notification for a journal entry
   */
  static async createJournalEntryNotification(entry: { id: string; room_code: string; is_secret: boolean; title: string }, currentUser: User) {
    const roomCode = entry.room_code;
    const users = await User.filter({ room_code: roomCode });
    const partner = users.find(u => u.id !== currentUser.id);
    
    if (!partner) return null;
    
    // Don't notify for secret entries
    if (entry.is_secret) return null;
    
    return BaseNotificationService.create({
      recipient_email: partner.email,
      type: 'new_entry',
      title: 'New Journal Entry',
      message: `${currentUser.full_name} added a new journal entry: "${entry.title}"`,
      room_code: roomCode,
      related_id: entry.id,
      related_type: 'journal_entry',
      action_url: '/journal'
    });
  }
  
  /**
   * Create a notification for a romantic plan
   */
  static async createRomanticPlanNotification(plan: { id: string; room_code: string; is_secret: boolean; title: string }, currentUser: User) {
    const roomCode = plan.room_code;
    const users = await User.filter({ room_code: roomCode });
    const partner = users.find(u => u.id !== currentUser.id);
    
    if (!partner) return null;
    
    // Don't notify for secret plans
    if (plan.is_secret) return null;
    
    return BaseNotificationService.create({
      recipient_email: partner.email,
      type: 'romantic_plan',
      title: 'New Romantic Plan',
      message: `${currentUser.full_name} created a new plan: "${plan.title}"`,
      room_code: roomCode,
      related_id: plan.id,
      related_type: 'romantic_plan',
      action_url: '/romance'
    });
  }
  
  /**
   * Create a notification for a mood update
   */
  static async createMoodUpdateNotification(user: User, mood: string) {
    const roomCode = user.room_code;
    const users = await User.filter({ room_code: roomCode });
    const partner = users.find(u => u.id !== user.id);
    
    if (!partner) return null;
    
    return BaseNotificationService.create({
      recipient_email: partner.email,
      type: 'mood_update',
      title: 'Mood Update',
      message: `${user.full_name}'s mood is now ${mood}`,
      room_code: roomCode,
      related_id: user.id,
      related_type: 'user_profile',
      action_url: '/dashboard'
    });
  }
  
  /**
   * Create a notification for a room change request
   */
  static async createRoomChangeRequestNotification(user: User, newRoomCode: string) {
    const roomCode = user.room_code;
    const users = await User.filter({ room_code: roomCode });
    const partner = users.find(u => u.id !== user.id);
    
    if (!partner) return null;
    
    return BaseNotificationService.create({
      recipient_email: partner.email,
      type: 'room_change_request',
      title: 'Room Change Request',
      message: `${user.full_name} wants to change your room code to "${newRoomCode}"`,
      room_code: roomCode,
      related_id: user.id,
      related_type: 'user_profile',
      action_url: '/profile'
    });
  }
  
  /**
   * Create a notification for a join request approval
   */
  static async createJoinApprovalNotification(approver: User, requestorEmail: string, roomCode: string) {
    return BaseNotificationService.create({
      recipient_email: requestorEmail,
      type: 'join_approved',
      title: 'Join Request Approved',
      message: `${approver.full_name} has approved your request to join room "${roomCode}"`,
      room_code: roomCode,
      related_id: approver.id,
      related_type: 'join_request',
      action_url: '/setup'
    });
  }
}