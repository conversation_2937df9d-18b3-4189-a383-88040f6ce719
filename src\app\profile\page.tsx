"use client";
import React, { useState, useEffect } from "react";
import { User, Notification, JournalEntry, RomanticPlan, Room } from "@/src/lib/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Switch } from "@/src/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { Calendar } from "@/src/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover";
import { User as UserIcon, Heart, Settings, Save, LogOut, Bell, Check, X, Upload, Eye, EyeOff, Trash2, UserX, Calendar as CalendarIcon } from "lucide-react";
import { format, set } from 'date-fns';

export default function Profile() {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [showPin, setShowPin] = useState(false);
  const [partner, setPartner] = useState(null);
  const [, setJoinRequestNotifications] = useState([]);
  const [showRoomChangeDialog, setShowRoomChangeDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDeactivateDialog, setShowDeactivateDialog] = useState(false);
  const [processingRoomChange, setProcessingRoomChange] = useState(false);
  
  const [deactivationDate, setDeactivationDate] = useState(new Date());
  const [deactivationHour, setDeactivationHour] = useState('0');
  const [deactivationMinute, setDeactivationMinute] = useState('0');

  const [, setPendingJoinRequests] = useState([]);
  const [joinRequests, setJoinRequests] = useState([]);

  const [profile, setProfile] = useState({
    full_name: '',
    partner_name: '',
    room_code: '',
    user_pin: '',
    avatar_url: '',
    current_mood: 'neutral',
    notification_preferences: {
      email_digest: true,
      partner_notifications: true,
      push_notifications: true
    }
  });

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    setIsLoading(true);
    try {
      const userData = await User.me();
      setUser(userData);
      
      const roomCode = sessionStorage.getItem('roomCode');
      if (roomCode) {
        const usersInRoom = await User.filter({ room_code: roomCode });
        const partnerData = usersInRoom.find(u => u.email !== userData.email);
        setPartner(partnerData);
      }

      const requests = await Notification.filter({
        recipient_email: userData.email,
        type: 'join_request'
      });
      setJoinRequestNotifications(requests.filter(r => !r.is_read));
      setJoinRequests(requests);
      setPendingJoinRequests(requests.filter(r => !r.is_read));

      setProfile({
        full_name: userData.full_name || '',
        partner_name: userData.partner_name || '',
        room_code: userData.room_code || roomCode || '',
        user_pin: userData.user_pin || '',
        avatar_url: userData.avatar_url || '',
        current_mood: userData.current_mood || 'neutral',
        notification_preferences: userData.notification_preferences || {
          email_digest: true,
          partner_notifications: true,
          push_notifications: true
        }
      });
    } catch (error) {
      console.error('Error loading user:', error);
    }
    setIsLoading(false);
  };

  const handleAvatarUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploadingAvatar(true);
    try {
      // Simulate upload - replace with actual upload logic
      const fileUrl = URL.createObjectURL(file);
      setProfile(prev => ({ ...prev, avatar_url: fileUrl }));
      console.log('Avatar uploaded successfully!');
    } catch (error) {
      console.error('Error uploading avatar:', error);
    }
    setUploadingAvatar(false);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const originalRoomCode = user.room_code;
      const newRoomCode = profile.room_code;

      if (originalRoomCode !== newRoomCode && partner) {
        setShowRoomChangeDialog(true);
        setIsSaving(false);
        return;
      }

      await User.updateMyUserData(profile);
      console.log("Profile updated successfully!");
      await loadUser(); 
    } catch (error) {
      console.error('Error saving profile:', error);
    }
    setIsSaving(false);
  };

  const handleRoomChangeRequest = async () => {
    setProcessingRoomChange(true);
    try {
      const newRoomCode = profile.room_code;
      
      await User.updateMyUserData({ 
        ...profile, 
        pending_room_change: newRoomCode, 
        room_code: user.room_code
      });
      
      await Notification.create({
        recipient_email: partner.email,
        type: 'room_change_request',
        title: 'Room Change Request',
        message: `${profile.full_name} wants to change your room code to "${newRoomCode}".`,
        room_code: user.room_code,
        related_id: user.id,
        related_type: 'user_profile'
      });
      
      setShowRoomChangeDialog(false);
      console.log("Room change request sent to your partner!");
      await loadUser();
    } catch (error) {
      console.error('Error sending room change request:', error);
    }
    setProcessingRoomChange(false);
  };

  const handleRoomChangeResponse = async (accept) => {
    const newRoomCode = user.pending_room_change;
    if (!newRoomCode || !partner) return;

    try {
      if (accept) {
        await User.update(user.id, { room_code: newRoomCode, pending_room_change: null });
        await User.update(partner.id, { room_code: newRoomCode });
        sessionStorage.setItem('roomCode', newRoomCode);
        console.log('Room code updated successfully!');
      } else {
        await User.update(user.id, { pending_room_change: null });
        console.log('Room code change request rejected.');
      }
      await loadUser();
    } catch(error) {
      console.error('Error handling room change response:', error);
    }
  };

  const handleJoinRequestResponse = async (notificationId, approve) => {
    try {
      const joinRequest = joinRequests.find(n => n.id === notificationId);
      
      if (!joinRequest) {
        console.error('Join request not found.');
        return;
      }
      
      if (approve) {
        await Notification.create({
          recipient_email: joinRequest.related_id,
          type: 'join_approved',
          title: 'Join Request Approved',
          message: `Your request to join room "${joinRequest.room_code}" has been approved by ${user.full_name}.`,
          room_code: joinRequest.room_code,
          related_id: user.id,
          related_type: 'user_profile'
        });

        const rooms = await Room.filter({ room_code: joinRequest.room_code });
        if (rooms.length > 0) {
          await Room.update(rooms[0].id, { partner_email: joinRequest.related_id });
        }
        
        console.log('Join request approved!');
      } else {
        console.log('Join request rejected.');
      }
      
      await Notification.update(notificationId, { is_read: true });
      
      setJoinRequests(prev => prev.filter(n => n.id !== notificationId));
      setPendingJoinRequests(prev => prev.filter(n => n.id !== notificationId));
      setJoinRequestNotifications(prev => prev.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Error handling join request:', error);
    }
  };

  const handleDeactivateAccount = async () => {
    try {
      const deactivateUntil = set(deactivationDate, {
        hours: parseInt(deactivationHour),
        minutes: parseInt(deactivationMinute)
      });
      
      await User.updateMyUserData({
        is_online: false,
        account_status: 'deactivated',
        deactivate_until: deactivateUntil.toISOString()
      });
      
      sessionStorage.removeItem('roomCode');
      sessionStorage.removeItem('userPin');
      console.log(`Account deactivated until ${format(deactivateUntil, 'PPP p')}.`);
      setShowDeactivateDialog(false);
    } catch (error) {
      console.error('Error deactivating account:', error);
    }
  };

  const handleDeleteAccount = async () => {
    try {
      if (!user) {
        console.error('User data not found for deletion.');
        return;
      }
      const roomCode = sessionStorage.getItem('roomCode');

      if (roomCode) {
        try {
          const userEntries = await JournalEntry.filter({ created_by: user.email, room_code: roomCode });
          for (const entry of userEntries) await JournalEntry.delete(entry.id);
        } catch (e) { console.warn("Could not delete journal entries:", e); }
        
        try {
          const userPlans = await RomanticPlan.filter({ created_by: user.email, room_code: roomCode });
          for (const plan of userPlans) await RomanticPlan.delete(plan.id);
        } catch (e) { console.warn("Could not delete romantic plans:", e); }
      }
      
      try {
        const userNotifications = await Notification.filter({ recipient_email: user.email });
        for (const notification of userNotifications) await Notification.delete(notification.id);
      } catch(e) { console.warn("Could not delete notifications:", e); }
      
      await User.delete(user.id);
      
      sessionStorage.removeItem('roomCode');
      sessionStorage.removeItem('userPin');
      console.log('Account and all data permanently deleted.');
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting account:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await User.updateMyUserData({ is_online: false, last_sync: new Date().toISOString() });
    } catch (error) {
      console.error('Error updating status on logout:', error);
    }
    
    sessionStorage.removeItem('roomCode');
    sessionStorage.removeItem('userPin');
  };

  const getInitials = (name) => {
    if (!name) return '';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse mx-auto mb-4"></div>
          <p className="text-purple-600 font-medium">Loading your profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 md:p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        <div className="text-center">
          <div className="relative inline-block mb-4">
            <Avatar className="w-20 h-20 mx-auto">
              <AvatarImage src={profile.avatar_url} />
              <AvatarFallback className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-2xl">
                {profile.full_name ? getInitials(profile.full_name) : <UserIcon className="w-10 h-10" />}
              </AvatarFallback>
            </Avatar>
            <Button
              size="icon"
              className="absolute -bottom-1 -right-1 rounded-full bg-purple-500 hover:bg-purple-600 w-8 h-8"
              onClick={() => document.getElementById('avatar-upload').click()}
              disabled={uploadingAvatar}
            >
              {uploadingAvatar ? (
                <div className="w-3 h-3 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <Upload className="w-3 h-3" />
              )}
            </Button>
            <input
              id="avatar-upload"
              type="file"
              accept="image/*"
              onChange={handleAvatarUpload}
              className="hidden"
            />
          </div>
          <h1 className="text-3xl font-bold gradient-text mb-3">Your Profile</h1>
          <p className="text-purple-600">
            Customize your experience and connection settings
          </p>
        </div>

        {user?.pending_room_change && (
           <Card className="card-premium border-yellow-300">
             <CardHeader>
               <CardTitle className="flex items-center gap-2 text-yellow-900">
                 <Bell className="w-5 h-5" />
                 Room Change Approval Needed
               </CardTitle>
             </CardHeader>
             <CardContent className="space-y-4">
                <p className="text-sm text-yellow-800">
                    Your partner has requested to change your shared room code to: 
                    <span className="font-semibold block mt-1">&ldquo;{user.pending_room_change}&rdquo;</span>
                </p>
                <div className="flex justify-end gap-3">
                    <Button 
                      variant="outline" 
                      onClick={() => handleRoomChangeResponse(false)}
                    >
                      <X className="w-4 h-4 mr-2" /> Reject
                    </Button>
                    <Button 
                      className="bg-green-500 hover:bg-green-600" 
                      onClick={() => handleRoomChangeResponse(true)}
                    >
                      <Check className="w-4 h-4 mr-2" /> Accept
                    </Button>
                </div>
             </CardContent>
           </Card>
        )}

        {joinRequests.filter(request => !request.is_read).length > 0 && joinRequests.filter(request => !request.is_read).map(request => {
          const messageParts = request.message.split(' ');
          const requestingUserName = messageParts[0];
          
          return (
            <Card key={request.id} className="card-premium border-blue-300">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-900">
                  <Bell className="w-5 h-5" />
                  New User Join Request
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-blue-800">
                  <span className="font-semibold">{requestingUserName}</span> wants to join your room 
                  <span className="font-semibold block mt-1">&ldquo;{request.room_code}&rdquo;</span>.
                  Do you want to approve this request?
                </p>
                <div className="flex justify-end gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => handleJoinRequestResponse(request.id, false)}
                  >
                    <X className="w-4 h-4 mr-2" /> Reject
                  </Button>
                  <Button 
                    className="bg-green-500 hover:bg-green-600" 
                    onClick={() => handleJoinRequestResponse(request.id, true)}
                  >
                    <Check className="w-4 h-4 mr-2" /> Approve
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}

        <Card className="card-premium">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-900">
              <Settings className="w-5 h-5" />
              Profile Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="email" className="text-purple-800 font-medium">Email</Label>
                <Input
                  id="email"
                  value={user?.email || ''}
                  disabled
                  className="mt-1 bg-gray-50"
                />
                <p className="text-xs text-purple-500 mt-1">Email cannot be changed</p>
              </div>

              <div>
                <Label htmlFor="full_name" className="text-purple-800 font-medium">Your Full Name</Label>
                <Input
                  id="full_name"
                  value={profile.full_name}
                  onChange={(e) => setProfile(prev => ({ ...prev, full_name: e.target.value }))}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="partner_name" className="text-purple-800 font-medium">Partner&apos;s Name</Label>
                <Input
                  id="partner_name"
                  value={profile.partner_name}
                  onChange={(e) => setProfile(prev => ({ ...prev, partner_name: e.target.value }))}
                  placeholder="Your partner&apos;s name..."
                  className="mt-1"
                />
              </div>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg space-y-4">
              <h3 className="font-semibold text-purple-900 flex items-center gap-2">
                <Heart className="w-4 h-4" />
                Connection Settings
              </h3>
              
              <div>
                <Label htmlFor="room_code" className="text-purple-800 font-medium">Room Code</Label>
                <Input
                  id="room_code"
                  value={profile.room_code}
                  onChange={(e) => setProfile(prev => ({ ...prev, room_code: e.target.value }))}
                  placeholder="Shared room code..."
                  className="mt-1"
                />
                <p className="text-xs text-purple-600 mt-1">
                  Changing this will require your partner&apos;s approval.
                </p>
              </div>

               <div>
                <Label htmlFor="user_pin" className="text-purple-800 font-medium">Your Personal Pin</Label>
                <div className="relative">
                  <Input
                    id="user_pin"
                    type={showPin ? "text" : "password"}
                    value={profile.user_pin}
                    onChange={(e) => setProfile(prev => ({...prev, user_pin: e.target.value.slice(0, 8)}))}
                    placeholder="8-digit pin"
                    maxLength={8}
                    className="mt-1 pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPin(!showPin)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-500 hover:text-purple-700"
                  >
                    {showPin ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <div>
                <Label className="text-purple-800 font-medium">Current Mood</Label>
                <Select 
                  value={profile.current_mood} 
                  onValueChange={(value) => setProfile(prev => ({ ...prev, current_mood: value }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="very_happy">😊 Very Happy</SelectItem>
                    <SelectItem value="happy">🙂 Happy</SelectItem>
                    <SelectItem value="neutral">😐 Neutral</SelectItem>
                    <SelectItem value="sad">😔 Sad</SelectItem>
                    <SelectItem value="frustrated">😤 Frustrated</SelectItem>
                    <SelectItem value="angry">😠 Angry</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-purple-900">Notification Preferences</h3>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email_digest" className="text-purple-800 font-medium">Weekly Email Digest</Label>
                  <p className="text-xs text-purple-600">Receive weekly relationship insights</p>
                </div>
                <Switch
                  id="email_digest"
                  checked={profile.notification_preferences.email_digest}
                  onCheckedChange={(checked) => 
                    setProfile(prev => ({
                      ...prev,
                      notification_preferences: {
                        ...prev.notification_preferences,
                        email_digest: checked
                      }
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="partner_notifications" className="text-purple-800 font-medium">Partner Activity Notifications</Label>
                  <p className="text-xs text-purple-600">Get notified when partner posts</p>
                </div>
                <Switch
                  id="partner_notifications"
                  checked={profile.notification_preferences.partner_notifications}
                  onCheckedChange={(checked) => 
                    setProfile(prev => ({
                      ...prev,
                      notification_preferences: {
                        ...prev.notification_preferences,
                        partner_notifications: checked
                      }
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push_notifications" className="text-purple-800 font-medium">Push Notifications</Label>
                  <p className="text-xs text-purple-600">Real-time notifications</p>
                </div>
                <Switch
                  id="push_notifications"
                  checked={profile.notification_preferences.push_notifications}
                  onCheckedChange={(checked) => 
                    setProfile(prev => ({
                      ...prev,
                      notification_preferences: {
                        ...prev.notification_preferences,
                        push_notifications: checked
                      }
                    }))
                  }
                />
              </div>
            </div>

            <div className="border-t pt-4 space-y-4">
              <h3 className="font-semibold text-red-900">Account Management</h3>
              <div className="flex flex-col gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeactivateDialog(true)}
                  className="w-full border-yellow-300 text-yellow-700 hover:bg-yellow-50"
                >
                  <UserX className="w-4 h-4 mr-2" />
                  Temporarily Deactivate Account
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                  className="w-full"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Permanently Delete Account
                </Button>
              </div>
            </div>

            <div className="flex justify-between items-center pt-4">
              <Button
                variant="outline"
                onClick={handleLogout}
                className="flex items-center gap-2"
              >
                <LogOut className="w-4 h-4" />
                Logout
              </Button>
              <Button
                onClick={handleSave}
                disabled={isSaving}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              >
                {isSaving ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Saving...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Save className="w-4 h-4" />
                    Save Changes
                  </div>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Dialog open={showRoomChangeDialog} onOpenChange={setShowRoomChangeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Room Code Change</DialogTitle>
            <DialogDescription>
              Are you sure you want to request changing the room code from &ldquo;{user?.room_code}&rdquo; to &ldquo;{profile.room_code}&rdquo;? 
              Your partner will need to approve this change.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRoomChangeDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleRoomChangeRequest}
              disabled={processingRoomChange}
              className="bg-gradient-to-r from-purple-500 to-pink-500"
            >
              {processingRoomChange ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Sending...
                </div>
              ) : (
                'Send Request'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showDeactivateDialog} onOpenChange={setShowDeactivateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Temporarily Deactivate Account</DialogTitle>
            <DialogDescription>
              Select the date and time you would like your account to be deactivated until. You can return anytime by logging back in.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {format(deactivationDate, 'PPP')}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={deactivationDate}
                  onSelect={setDeactivationDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="deactivation-hour">Hour</Label>
                <Select value={deactivationHour} onValueChange={setDeactivationHour}>
                  <SelectTrigger>
                    <SelectValue/>
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => (
                      <SelectItem key={i} value={String(i)}>{String(i).padStart(2, '0')}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="deactivation-minute">Minute</Label>
                <Select value={deactivationMinute} onValueChange={setDeactivationMinute}>
                   <SelectTrigger>
                    <SelectValue/>
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 60 }, (_, i) => (
                      <SelectItem key={i} value={String(i)}>{String(i).padStart(2, '0')}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeactivateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleDeactivateAccount} className="bg-yellow-500 hover:bg-yellow-600">
              Deactivate Account
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Permanently Delete Account</DialogTitle>
            <DialogDescription>
              This action cannot be undone. All your data including journal entries, romantic plans, and account information will be permanently deleted.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={handleDeleteAccount}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Permanently Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}