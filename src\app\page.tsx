"use client";
import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Heart, Shield, Sparkles, Users, ArrowRight, MessageSquare, TrendingUp, Calendar } from "lucide-react";

export default function Home() {
  const features = [
    {
      icon: MessageSquare,
      title: "Real-Time Journaling",
      description: "Share thoughts, feelings, and experiences instantly with your partner in a safe, private space."
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description: "Your conversations are protected with room-based encryption. Only you and your partner have access."
    },
    {
      icon: TrendingUp,
      title: "Growth Tracking",
      description: "Monitor your relationship patterns, celebrate progress, and identify areas for growth together."
    },
    {
      icon: Calendar,
      title: "Romance Planning",
      description: "Plan surprises, organize dates, and create magical moments with our built-in love lab."
    },
    {
      icon: Heart,
      title: "Emotional Intelligence",
      description: "Track moods, practice gratitude, and build deeper emotional connection through guided prompts."
    },
    {
      icon: Spark<PERSON>,
      title: "Weekly Insights",
      description: "Receive personalized relationship insights and growth recommendations delivered weekly."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-100 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-pink-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-300/10 to-pink-300/10 rounded-full blur-2xl animate-pulse-romantic"></div>
      </div>

      <nav className="bg-white/70 backdrop-blur-xl border-b border-romantic-200/50 sticky top-0 z-50 shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-18">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-romantic rounded-2xl flex items-center justify-center shadow-romantic animate-pulse-romantic">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-display font-bold gradient-text">
                Between Us
              </span>
            </div>
            <Link href="/landing">
              <Button variant="romantic" size="lg" className="shadow-romantic hover:shadow-glow">
                Enter Your Space
                <Heart className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      <section className="relative overflow-hidden section-padding-lg px-4">
        <div className="container-romantic text-center">
          <div className="inline-block p-6 bg-white/80 backdrop-blur-sm rounded-3xl shadow-romantic mb-8 animate-fade-in-scale">
            <Heart className="w-20 h-20 text-romantic-500 animate-pulse-romantic" />
          </div>
          <h1 className="text-hero font-display mb-8 animate-fade-in-up">
            <span className="gradient-text block mb-2">
              Strengthen Your Bond
            </span>
            <span className="text-romantic-gradient text-4xl md:text-5xl font-medium">
              One Heart at a Time
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-up" style={{animationDelay: '0.2s'}}>
            A private, real-time emotional journaling platform designed to help couples communicate better,
            grow together, and build deeper intimacy through thoughtful reflection and meaningful connection.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up" style={{animationDelay: '0.4s'}}>
            <Link href="/landing">
              <Button variant="romantic" size="xl" className="text-lg font-semibold shadow-romantic hover:shadow-glow group">
                Start Your Journey Together
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Shield className="w-4 h-4 text-green-500" />
                <span>Secure & Private</span>
              </div>
              <div className="flex items-center gap-1">
                <Heart className="w-4 h-4 text-romantic-500" />
                <span>Free to use</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="section-padding px-4 bg-white/40 backdrop-blur-sm relative">
        <div className="container-romantic">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-display font-bold text-gray-800 mb-6">
              Everything You Need for a
              <span className="gradient-text block">Healthier Relationship</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Replace reactive conflict with reflective connection through our comprehensive suite of relationship tools designed with love and intention.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="card-romantic hover:scale-105 hover-lift group animate-fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-8 text-center">
                  <div className="w-18 h-18 bg-gradient-romantic rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-romantic group-hover:shadow-glow transition-all duration-300 group-hover:scale-110">
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-4 font-display">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 px-4">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-purple-900 mb-4">How It Works</h2>
            <p className="text-xl text-purple-600">Simple steps to start your emotional journey together</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-purple-900 mb-4">1. Create Your Private Room</h3>
              <p className="text-purple-700">Set up a secure, shared space with a unique room code and personal pins.</p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <MessageSquare className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-purple-900 mb-4">2. Share & Reflect</h3>
              <p className="text-purple-700">Journal your thoughts, express gratitude, and communicate openly in real-time.</p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-pink-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-purple-900 mb-4">3. Grow Together</h3>
              <p className="text-purple-700">Track patterns, celebrate progress, and build a stronger, more connected relationship.</p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 px-4 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center text-white">
          <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Relationship?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of couples who have strengthened their bond through meaningful, intentional communication.
          </p>
          <Link href="/landing">
            <Button size="lg" className="text-lg px-8 py-4 bg-white text-purple-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all duration-300">
              Begin Your Journey Today
              <Heart className="w-5 h-5 ml-2" />
            </Button>
          </Link>
        </div>
      </section>

      <footer className="bg-purple-900 text-white py-12 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Heart className="w-6 h-6 text-pink-400" />
            <span className="text-xl font-bold">Between Us</span>
          </div>
          <p className="text-purple-200 mb-4">
            Strengthening relationships through thoughtful communication and emotional intelligence.
          </p>
          <p className="text-purple-300 text-sm">
            &copy; {new Date().getFullYear()} Between Us. All rights reserved. Built with ❤️ for couples everywhere.
          </p>
        </div>
      </footer>
    </div>
  );
}