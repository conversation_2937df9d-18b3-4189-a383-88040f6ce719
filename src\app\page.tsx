"use client";
import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Heart, Shield, Sparkles, Users, ArrowRight, MessageSquare, TrendingUp, Calendar } from "lucide-react";

export default function Home() {
  const features = [
    {
      icon: MessageSquare,
      title: "Real-Time Journaling",
      description: "Share thoughts, feelings, and experiences instantly with your partner in a safe, private space."
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description: "Your conversations are protected with room-based encryption. Only you and your partner have access."
    },
    {
      icon: TrendingUp,
      title: "Growth Tracking",
      description: "Monitor your relationship patterns, celebrate progress, and identify areas for growth together."
    },
    {
      icon: Calendar,
      title: "Romance Planning",
      description: "Plan surprises, organize dates, and create magical moments with our built-in love lab."
    },
    {
      icon: Heart,
      title: "Emotional Intelligence",
      description: "Track moods, practice gratitude, and build deeper emotional connection through guided prompts."
    },
    {
      icon: Spark<PERSON>,
      title: "Weekly Insights",
      description: "Receive personalized relationship insights and growth recommendations delivered weekly."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-100">
      <nav className="bg-white/80 backdrop-blur-md border-b border-purple-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <Heart className="w-8 h-8 text-pink-500" />
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-500 text-transparent bg-clip-text">
                Between Us
              </span>
            </div>
            <Link href="/landing">
              <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                Enter Your Space
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      <section className="relative overflow-hidden py-20 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="inline-block p-4 bg-white/70 rounded-full shadow-lg mb-6">
            <Heart className="w-16 h-16 text-pink-500" />
          </div>
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            <span className="bg-gradient-to-r from-purple-600 to-pink-500 text-transparent bg-clip-text">
              Strengthen Your Bond
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-purple-700 mb-8 max-w-3xl mx-auto leading-relaxed">
            A private, real-time emotional journaling platform designed to help couples communicate better, 
            grow together, and build deeper intimacy through thoughtful reflection.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/landing">
              <Button size="lg" className="text-lg px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-xl transition-all duration-300">
                Start Your Journey Together
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
            <p className="text-sm text-purple-600">
              Free to use • Secure & Private • No signup required
            </p>
          </div>
        </div>
      </section>

      <section className="py-20 px-4 bg-white/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-purple-900 mb-4">
              Everything You Need for a Healthier Relationship
            </h2>
            <p className="text-xl text-purple-600 max-w-2xl mx-auto">
              Replace reactive conflict with reflective connection through our comprehensive suite of relationship tools.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-purple-900 mb-4">{feature.title}</h3>
                  <p className="text-purple-700 leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 px-4">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-purple-900 mb-4">How It Works</h2>
            <p className="text-xl text-purple-600">Simple steps to start your emotional journey together</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-purple-900 mb-4">1. Create Your Private Room</h3>
              <p className="text-purple-700">Set up a secure, shared space with a unique room code and personal pins.</p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <MessageSquare className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-purple-900 mb-4">2. Share & Reflect</h3>
              <p className="text-purple-700">Journal your thoughts, express gratitude, and communicate openly in real-time.</p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-pink-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-purple-900 mb-4">3. Grow Together</h3>
              <p className="text-purple-700">Track patterns, celebrate progress, and build a stronger, more connected relationship.</p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 px-4 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center text-white">
          <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Relationship?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of couples who have strengthened their bond through meaningful, intentional communication.
          </p>
          <Link href="/landing">
            <Button size="lg" className="text-lg px-8 py-4 bg-white text-purple-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all duration-300">
              Begin Your Journey Today
              <Heart className="w-5 h-5 ml-2" />
            </Button>
          </Link>
        </div>
      </section>

      <footer className="bg-purple-900 text-white py-12 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Heart className="w-6 h-6 text-pink-400" />
            <span className="text-xl font-bold">Between Us</span>
          </div>
          <p className="text-purple-200 mb-4">
            Strengthening relationships through thoughtful communication and emotional intelligence.
          </p>
          <p className="text-purple-300 text-sm">
            &copy; {new Date().getFullYear()} Between Us. All rights reserved. Built with ❤️ for couples everywhere.
          </p>
        </div>
      </footer>
    </div>
  );
}