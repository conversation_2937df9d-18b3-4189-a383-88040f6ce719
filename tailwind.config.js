/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'display': ['Playfair Display', 'Georgia', 'serif'],
      },
      colors: {
        romantic: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        },
        purple: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        accent: {
          primary: '#8B5CF6',
          secondary: '#EC4899',
          tertiary: '#F472B6',
        }
      },
      backgroundImage: {
        'gradient-romantic': 'linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%)',
        'gradient-romantic-soft': 'linear-gradient(135deg, #E8D5FF 0%, #FFE4E6 100%)',
        'gradient-hero': 'linear-gradient(135deg, #667eea 0%, #764ba2 25%, #8B5CF6 50%, #EC4899 75%, #f093fb 100%)',
        'gradient-glass': 'linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)',
      },
      boxShadow: {
        'romantic': '0 8px 32px rgba(236, 72, 153, 0.15), 0 4px 16px rgba(139, 92, 246, 0.1)',
        'romantic-lg': '0 20px 40px rgba(236, 72, 153, 0.2), 0 8px 24px rgba(139, 92, 246, 0.15)',
        'glow': '0 0 20px rgba(139, 92, 246, 0.3), 0 0 40px rgba(236, 72, 153, 0.2)',
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'soft': '0 4px 6px -1px rgba(139, 92, 246, 0.05), 0 2px 4px -1px rgba(236, 72, 153, 0.03)',
      },
      animation: {
        'float': 'float 3s ease-in-out infinite',
        'pulse-romantic': 'pulse-romantic 2s infinite',
        'fade-in-up': 'fade-in-up 0.6s ease-out',
        'fade-in-scale': 'fade-in-scale 0.4s ease-out',
        'shimmer': 'shimmer 2s infinite',
        'spin-romantic': 'spin-romantic 1.5s linear infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'pulse-romantic': {
          '0%, 100%': {
            transform: 'scale(1)',
            boxShadow: '0 0 0 0 rgba(236, 72, 153, 0.4)',
          },
          '50%': {
            transform: 'scale(1.05)',
            boxShadow: '0 0 0 10px rgba(236, 72, 153, 0)',
          },
        },
        'fade-in-up': {
          from: {
            opacity: '0',
            transform: 'translateY(30px)',
          },
          to: {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        'fade-in-scale': {
          from: {
            opacity: '0',
            transform: 'scale(0.9)',
          },
          to: {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'spin-romantic': {
          '0%': {
            transform: 'rotate(0deg)',
            borderTopColor: '#EC4899',
            borderRightColor: '#8B5CF6',
          },
          '50%': {
            transform: 'rotate(180deg)',
            borderTopColor: '#8B5CF6',
            borderRightColor: '#EC4899',
          },
          '100%': {
            transform: 'rotate(360deg)',
            borderTopColor: '#EC4899',
            borderRightColor: '#8B5CF6',
          },
        },
      },
      backdropBlur: {
        'xs': '2px',
      },
      borderRadius: {
        'xl': '12px',
        '2xl': '16px',
        '3xl': '24px',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
    },
  },
  plugins: [],
}