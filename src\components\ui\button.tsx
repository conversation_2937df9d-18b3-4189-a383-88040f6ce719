import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/src/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-romantic-400/50 focus-visible:ring-offset-2 relative overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-romantic text-white shadow-romantic hover:shadow-romantic-lg hover:-translate-y-0.5 active:translate-y-0 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:-translate-x-full hover:before:translate-x-full before:transition-transform before:duration-500",
        destructive:
          "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 active:translate-y-0",
        outline:
          "border-2 border-romantic-200 bg-white/80 backdrop-blur-sm text-romantic-600 shadow-soft hover:bg-romantic-50 hover:border-romantic-300 hover:-translate-y-0.5 active:translate-y-0",
        secondary:
          "bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 shadow-soft hover:shadow-romantic hover:-translate-y-0.5 active:translate-y-0",
        ghost:
          "text-romantic-600 hover:bg-romantic-50 hover:text-romantic-700 rounded-xl",
        link: "text-romantic-600 underline-offset-4 hover:underline hover:text-romantic-700",
        glass:
          "bg-white/25 backdrop-blur-md border border-white/20 text-gray-700 shadow-glass hover:bg-white/40 hover:border-romantic-200/50 hover:-translate-y-0.5 active:translate-y-0",
        romantic:
          "bg-gradient-to-r from-romantic-500 to-romantic-600 text-white shadow-romantic hover:shadow-glow hover:-translate-y-1 active:translate-y-0 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent before:-translate-x-full hover:before:translate-x-full before:transition-transform before:duration-700",
      },
      size: {
        default: "h-11 px-6 py-2.5 has-[>svg]:px-5",
        sm: "h-9 rounded-lg gap-1.5 px-4 text-sm has-[>svg]:px-3",
        lg: "h-12 rounded-xl px-8 text-base has-[>svg]:px-6",
        icon: "size-11 rounded-xl",
        xl: "h-14 rounded-2xl px-10 text-lg has-[>svg]:px-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
