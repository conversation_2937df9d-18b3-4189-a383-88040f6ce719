import React from 'react';
import { Card, CardContent } from "@/src/components/ui/card";
import { Heart, MessageSquare, Sparkles, TrendingUp } from "lucide-react";

export default function QuickStats({ entries, romanticPlans }) {
  const totalEntries = entries.length;
  const praiseEntries = entries.filter(e => e.entry_type === 'praise' || e.entry_type === 'gratitude');
  const issueEntries = entries.filter(e => ['minor_issue', 'habitual', 'emotional_hurt', 'needs_discussion'].includes(e.entry_type));
  const unresolvedIssues = issueEntries.filter(e => !e.is_resolved).length;
  const activePlans = romanticPlans.filter(p => p.status !== 'executed' && p.status !== 'cancelled').length;
  
  const praiseRatio = issueEntries.length > 0 ? (praiseEntries.length / issueEntries.length * 100).toFixed(0) : 100;

  const stats = [
    {
      title: "Total Entries",
      value: totalEntries,
      icon: MessageSquare,
      gradient: "from-purple-400 to-purple-600",
      description: "This month"
    },
    {
      title: "Praise Ratio",
      value: `${praiseRatio}%`,
      icon: Heart,
      gradient: "from-pink-400 to-pink-600",
      description: "Positive vs concerns"
    },
    {
      title: "Open Issues",
      value: unresolvedIssues,
      icon: TrendingUp,
      gradient: "from-blue-400 to-blue-600",
      description: "Need attention"
    },
    {
      title: "Love Plans",
      value: activePlans,
      icon: Sparkles,
      gradient: "from-yellow-400 to-yellow-600",
      description: "In progress"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <Card key={index} className="card-premium hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">{stat.title}</p>
                <p className="text-2xl font-bold text-purple-900 mt-1">{stat.value}</p>
                <p className="text-xs text-purple-500 mt-1">{stat.description}</p>
              </div>
              <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.gradient} bg-opacity-20`}>
                <stat.icon className={`w-6 h-6 text-purple-700`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}