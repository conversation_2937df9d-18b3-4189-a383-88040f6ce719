import { JournalEntry } from "../entities/all";

export class VaultService {
  // Storage key for vault access records
  private static readonly VAULT_ACCESS_KEY = 'betweenus_vault_access';
  
  /**
   * Add an entry to the vault
   */
  static async addToVault(entryId: string): Promise<void> {
    const entry = await JournalEntry.filter({ id: entryId });
    if (entry.length === 0) {
      throw new Error('Entry not found');
    }
    
    await JournalEntry.update(entryId, { is_vault: true });
  }
  
  /**
   * Remove an entry from the vault
   */
  static async removeFromVault(entryId: string): Promise<void> {
    const entry = await JournalEntry.filter({ id: entryId });
    if (entry.length === 0) {
      throw new Error('Entry not found');
    }
    
    await JournalEntry.update(entryId, { is_vault: false });
  }
  
  /**
   * Request access to a vault entry
   */
  static async requestAccess(entryId: string, userId: string): Promise<boolean> {
    // In a real app, this would send a request to the partner
    // For demo purposes, we'll simulate this with localStorage
    
    const accessRequests = this.getAccessRequests();
    
    if (!accessRequests[entryId]) {
      accessRequests[entryId] = {
        requestedBy: userId,
        approved: false,
        requestDate: new Date().toISOString()
      };
    }
    
    this.saveAccessRequests(accessRequests);
    return true;
  }
  
  /**
   * Approve access to a vault entry
   */
  static async approveAccess(entryId: string): Promise<boolean> {
    const accessRequests = this.getAccessRequests();
    
    if (accessRequests[entryId]) {
      accessRequests[entryId].approved = true;
      accessRequests[entryId].approvedDate = new Date().toISOString();
      this.saveAccessRequests(accessRequests);
      return true;
    }
    
    return false;
  }
  
  /**
   * Check if a user has access to a vault entry
   */
  static async hasAccess(entryId: string, userId: string): Promise<boolean> {
    const accessRequests = this.getAccessRequests();
    
    // For demo purposes, we'll check if the user is the one who requested access
    // or if they're the partner of the requester
    if (accessRequests[entryId]) {
      if (accessRequests[entryId].approved) {
        return true;
      }
      
      if (accessRequests[entryId].requestedBy === userId) {
        // The requester always has access to their own request
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * Get all vault entries
   */
  static async getVaultEntries(roomCode: string): Promise<JournalEntry[]> {
    return await JournalEntry.filter({ room_code: roomCode, is_vault: true });
  }
  
  /**
   * Get all accessible vault entries for a user
   */
  static async getAccessibleVaultEntries(roomCode: string, userId: string): Promise<JournalEntry[]> {
    const vaultEntries = await this.getVaultEntries(roomCode);
    const accessibleEntries = [];
    
    for (const entry of vaultEntries) {
      if (await this.hasAccess(entry.id, userId)) {
        accessibleEntries.push(entry);
      }
    }
    
    return accessibleEntries;
  }
  
  /**
   * Get all access requests
   */
  private static getAccessRequests(): Record<string, unknown> {
    if (typeof window === 'undefined') return {};
    
    try {
      const requests = localStorage.getItem(this.VAULT_ACCESS_KEY);
      return requests ? JSON.parse(requests) : {};
    } catch (error) {
      console.error('Error getting vault access requests:', error);
      return {};
    }
  }
  
  /**
   * Save access requests
   */
  private static saveAccessRequests(requests: Record<string, unknown>): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(this.VAULT_ACCESS_KEY, JSON.stringify(requests));
    } catch (error) {
      console.error('Error saving vault access requests:', error);
    }
  }
}