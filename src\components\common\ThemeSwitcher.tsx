import React from 'react';
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover";
import { Button } from "@/src/components/ui/button";
import { Palette } from "lucide-react";

const themes = [
    { name: 'default', label: 'Default', gradient: 'from-purple-500 to-pink-500' },
    { name: 'ocean', label: 'Ocean', gradient: 'from-blue-500 to-teal-500' },
    { name: 'sunrise', label: 'Sunrise', gradient: 'from-yellow-500 to-orange-500' },
    { name: 'forest', label: 'Forest', gradient: 'from-green-500 to-lime-600' }
];

export default function ThemeSwitcher({ setTheme: setTheme }: { setTheme: (theme: string) => void }) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="text-purple-600 hover:bg-purple-100">
          <Palette className="w-5 h-5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-40">
        <div className="space-y-2">
            <h4 className="font-medium text-sm text-purple-900">Change Theme</h4>
            {themes.map(theme => (
                <Button 
                    key={theme.name}
                    variant="ghost" 
                    className="w-full justify-start gap-2"
                    onClick={() => setTheme(theme.name)}
                >
                    <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${theme.gradient}`} />
                    {theme.label}
                </Button>
            ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}