"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, User } from "@/src/lib/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";
import { Badge } from "@/src/components/ui/badge";
import { Switch } from "@/src/components/ui/switch";
import { Label } from "@/src/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs";
import { Heart, Gift, Calendar, Plus, Sparkles, EyeOff } from "lucide-react";
import { format } from 'date-fns';

const planTypes = [
  { value: 'surprise', label: 'Surprise', icon: Gift },
  { value: 'date_idea', label: 'Date Idea', icon: Heart },
  { value: 'gift', label: 'Gift', icon: Gift },
  { value: 'love_letter', label: 'Love Letter', icon: Sparkles },
  { value: 'anniversary', label: 'Anniversary', icon: Calendar },
  { value: 'special_occasion', label: 'Special Occasion', icon: Sparkles },
];

const statusColors = {
  idea: 'bg-gray-100 text-gray-800',
  planning: 'bg-blue-100 text-blue-800',
  ready: 'bg-green-100 text-green-800',
  executed: 'bg-purple-100 text-purple-800',
  cancelled: 'bg-red-100 text-red-800'
};

export default function Romance() {
  const [plans, setPlans] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [newPlan, setNewPlan] = useState({
    title: '',
    description: '',
    plan_type: '',
    target_date: '',
    is_secret: true,
    budget_estimate: '',
    status: 'idea',
    notes: ''
  });

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    setIsLoading(true);
    try {
      const user = await User.me();
      setCurrentUser(user);
      const roomCode = sessionStorage.getItem('roomCode');
      if(roomCode){
        const data = await RomanticPlan.filter({ room_code: roomCode });
        setPlans(data);
      }
    } catch (error) {
      console.error("Error loading plans:", error);
    }
    setIsLoading(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!newPlan.title.trim() || !newPlan.description.trim() || !newPlan.plan_type) return;
        
    const roomCode = sessionStorage.getItem('roomCode');
    const savedPlan = await RomanticPlan.create({ ...newPlan, room_code: roomCode });
    
    // Create notification for new plan
    try {
      const { NotificationService } = await import('@/src/lib/services/NotificationService');
      await NotificationService.createRomanticPlanNotification(savedPlan, currentUser);
    } catch (error) {
      console.error("Error creating notification:", error);
    }
    
    setNewPlan({
      title: '',
      description: '',
      plan_type: '',
      target_date: '',
      is_secret: true,
      budget_estimate: '',
      status: 'idea',
      notes: ''
    });
    setShowForm(false);
    loadPlans();
  };

  const updatePlanStatus = async (planId, newStatus) => {
    await RomanticPlan.update(planId, { status: newStatus });
    loadPlans();
  };

  const visiblePlans = plans.filter(p => {
    if (p.is_secret && p.created_by !== currentUser?.email) {
      return false;
    }
    return true;
  });

  const activePlans = visiblePlans.filter(p => p.status !== 'executed' && p.status !== 'cancelled');
  const completedPlans = visiblePlans.filter(p => p.status === 'executed');

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-red-500 rounded-full animate-pulse mx-auto mb-4"></div>
          <p className="text-pink-600 font-medium">Loading your love lab...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 md:p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Heart className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold gradient-text mb-3">Love Lab</h1>
          <p className="text-pink-600 max-w-2xl mx-auto">
            Plan surprises, organize romantic gestures, and create magical moments together.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="flex justify-center">
          <Button
            onClick={() => setShowForm(!showForm)}
            className="bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Romantic Plan
          </Button>
        </div>

        {/* New Plan Form */}
        {showForm && (
          <Card className="card-premium">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-pink-900">Create Romantic Plan</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="title" className="text-pink-800 font-medium">Title</Label>
                    <Input
                      id="title"
                      value={newPlan.title}
                      onChange={(e) => setNewPlan(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Surprise dinner at home..."
                      className="mt-1"
                      required
                    />
                  </div>
                  <div>
                    <Label className="text-pink-800 font-medium">Plan Type</Label>
                    <Select
                       value={newPlan.plan_type}
                       onValueChange={(value) => setNewPlan(prev => ({ ...prev, plan_type: value }))}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="What kind of plan?" />
                      </SelectTrigger>
                      <SelectContent>
                        {planTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center gap-2">
                              <type.icon className="w-4 h-4" />
                              {type.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="target_date" className="text-pink-800 font-medium">Target Date</Label>
                    <Input
                      id="target_date"
                      type="date"
                      value={newPlan.target_date}
                      onChange={(e) => setNewPlan(prev => ({ ...prev, target_date: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="budget" className="text-pink-800 font-medium">Budget Estimate ($)</Label>
                    <Input
                      id="budget"
                      type="number"
                      value={newPlan.budget_estimate}
                      onChange={(e) => setNewPlan(prev => ({ ...prev, budget_estimate: parseFloat(e.target.value) || '' }))}
                      placeholder="0.00"
                      className="mt-1"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="description" className="text-pink-800 font-medium">Description</Label>
                  <Textarea
                    id="description"
                    value={newPlan.description}
                    onChange={(e) => setNewPlan(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your romantic plan in detail..."
                    className="mt-1 min-h-[100px]"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="notes" className="text-pink-800 font-medium">Additional Notes</Label>
                  <Textarea
                    id="notes"
                    value={newPlan.notes}
                    onChange={(e) => setNewPlan(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Shopping list, reminders, etc..."
                    className="mt-1"
                  />
                </div>
                <div className="flex items-center justify-between p-4 bg-pink-50 rounded-lg">
                  <div>
                    <Label htmlFor="secret" className="text-pink-800 font-medium">Keep Secret</Label>
                    <p className="text-xs text-pink-600">Hide from partner until executed</p>
                  </div>
                  <Switch
                    id="secret"
                    checked={newPlan.is_secret}
                    onCheckedChange={(checked) => setNewPlan(prev => ({ ...prev, is_secret: checked }))}
                  />
                </div>
                <div className="flex justify-end gap-3">
                  <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                    Cancel
                  </Button>
                  <Button
                     type="submit"
                    className="bg-gradient-to-r from-pink-500 to-red-500"
                  >
                    Create Plan
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Plans Tabs */}
        <Tabs defaultValue="active" className="space-y-6">
          <TabsList className="bg-white/70 backdrop-blur-sm">
            <TabsTrigger value="active">Active Plans ({activePlans.length})</TabsTrigger>
            <TabsTrigger value="completed">Completed ({completedPlans.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {activePlans.map((plan) => {
                const planType = planTypes.find(t => t.value === plan.plan_type);
                const Icon = planType?.icon || Heart;
                
                return (
                  <Card key={plan.id} className="card-premium">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-gradient-to-r from-pink-100 to-red-100">
                            <Icon className="w-5 h-5 text-pink-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-pink-900">{plan.title}</h3>
                            <div className="flex items-center flex-wrap gap-2 mt-1">
                              <Badge className={statusColors[plan.status]}>
                                {plan.status.replace('_', ' ')}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {planType?.label}
                              </Badge>
                              {plan.created_by === currentUser?.email && plan.is_secret && (
                                <Badge variant="outline" className="text-xs text-purple-600">
                                  <EyeOff className="w-3 h-3 mr-1" />
                                  Secret
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        {plan.target_date && (
                          <div className="text-right flex-shrink-0 ml-4">
                            <p className="text-sm font-medium text-pink-800">Target Date</p>
                            <p className="text-xs text-pink-600">
                              {format(new Date(plan.target_date), 'MMM d, yyyy')}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-pink-800">{plan.description}</p>
                      
                      {plan.notes && (
                        <div className="p-3 bg-pink-50 rounded-lg">
                          <p className="text-sm text-pink-700">{plan.notes}</p>
                        </div>
                      )}

                      {plan.budget_estimate > 0 && (
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-pink-600">Budget:</span>
                          <span className="font-medium text-pink-800">${plan.budget_estimate}</span>
                        </div>
                      )}

                      {plan.created_by === currentUser?.email && (
                        <div className="flex justify-end gap-2">
                          <Select
                            value={plan.status}
                            onValueChange={(value) => updatePlanStatus(plan.id, value)}
                          >
                            <SelectTrigger className="w-[130px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="idea">Idea</SelectItem>
                              <SelectItem value="planning">Planning</SelectItem>
                              <SelectItem value="ready">Ready</SelectItem>
                              <SelectItem value="executed">Executed</SelectItem>
                              <SelectItem value="cancelled">Cancelled</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}

              {activePlans.length === 0 && (
                <div className="text-center py-12 md:col-span-2">
                  <div className="w-20 h-20 bg-gradient-to-r from-pink-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Heart className="w-10 h-10 text-pink-500" />
                  </div>
                  <h3 className="text-xl font-semibold text-pink-900 mb-2">No active plans</h3>
                  <p className="text-pink-600 mb-4">Start planning beautiful moments together.</p>
                  <Button
                    onClick={() => setShowForm(true)}
                    className="bg-gradient-to-r from-pink-500 to-red-500"
                  >
                    Create First Plan
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="completed" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {completedPlans.map((plan) => {
                const planType = planTypes.find(t => t.value === plan.plan_type);
                const Icon = planType?.icon || Heart;
                
                return (
                  <Card key={plan.id} className="card-premium opacity-75">
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-gradient-to-r from-green-100 to-green-200">
                          <Icon className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-green-900">{plan.title}</h3>
                          <p className="text-sm text-green-600">
                            Completed on {format(new Date(plan.updated_date), 'MMM d, yyyy')}
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-green-800">{plan.description}</p>
                    </CardContent>
                  </Card>
                );
              })}
                 
              {completedPlans.length === 0 && (
                     <div className="text-center py-12 md:col-span-2">
                        <p className="text-pink-600">No completed plans yet.</p>
                     </div>
                 )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}