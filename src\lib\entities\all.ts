// Entity interfaces
export interface JournalEntry {
  id: string;
  content: string;
  entry_type: string;
  mood: string;
  is_draft: boolean;
  is_secret: boolean;
  is_vault: boolean;
  is_resolved: boolean;
  title?: string;
  author_email: string;
  created_by: string;
  created_date: string;
  partner_response?: string;
  response_date?: string;
  room_code: string;
}

export interface RomanticPlan {
  id: string;
  title: string;
  description: string;
  plan_type: string;
  target_date: string;
  is_secret: boolean;
  budget_estimate: string;
  status: string;
  notes: string;
  created_by: string;
  room_code: string;
  updated_date?: string;
}

export interface Notification {
  id: string;
  recipient_email: string;
  type: string;
  title: string;
  message: string;
  room_code: string;
  related_id?: string;
  related_type?: string;
  action_url?: string;
  is_read: boolean;
  created_date: string;
}

export interface Room {
  id: string;
  room_code: string;
  creator_email: string;
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  user_pin?: string;
  room_code?: string;
  partner_name?: string;
  current_mood?: string;
  notification_preferences?: Record<string, unknown>;
  is_online?: boolean;
  last_sync?: string;
  pending_room_change?: string;
}

// Helper function to generate unique IDs
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// Helper function to safely access localStorage
const getLocalStorage = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue;
  try {
    const value = localStorage.getItem(key);
    if (value) return JSON.parse(value);
    return defaultValue;
  } catch (error) {
    console.error(`Error accessing localStorage for key ${key}:`, error);
    return defaultValue;
  }
};

// Helper function to safely set localStorage
const setLocalStorage = <T>(key: string, value: T): void => {
  if (typeof window === 'undefined') return;
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error setting localStorage for key ${key}:`, error);
  }
};

// Helper function to filter objects based on partial match
const filterObjects = <T extends Record<string, unknown>>(
  objects: T[],
  params: Partial<T>
): T[] => {
  return objects.filter((obj) => {
    return Object.entries(params).every(([key, value]) => {
      return obj[key] === value;
    });
  });
};

// Implementation of JournalEntryService
export class JournalEntryService {
  private static readonly STORAGE_KEY = 'betweenus_journal_entries';

  static async filter(params: Partial<JournalEntry> & { updated_after?: string } = {}): Promise<JournalEntry[]> {
    const entries = getLocalStorage<JournalEntry[]>(this.STORAGE_KEY, []);
    let filtered = filterObjects(entries, params);
    
    // Filter by updated_after if provided
    if (params.updated_after) {
      const afterDate = new Date(params.updated_after);
      filtered = filtered.filter(entry => new Date(entry.updated_date) > afterDate);
    }
    
    return filtered;
  }
  
  static async create(data: Omit<JournalEntry, 'id'>): Promise<JournalEntry> {
    const entries = getLocalStorage<JournalEntry[]>(this.STORAGE_KEY, []);
    const newEntry: JournalEntry = {
      ...data as JournalEntry,
      id: generateId(),
      created_date: data.created_date || new Date().toISOString()
    };
    
    entries.push(newEntry);
    setLocalStorage(this.STORAGE_KEY, entries);
    return newEntry;
  }
  
  static async update(id: string, updates: Partial<JournalEntry>): Promise<JournalEntry> {
    const entries = getLocalStorage<JournalEntry[]>(this.STORAGE_KEY, []);
    const index = entries.findIndex(entry => entry.id === id);
    
    if (index === -1) {
      throw new Error(`Journal entry with id ${id} not found`);
    }
    
    const updatedEntry = { ...entries[index], ...updates };
    entries[index] = updatedEntry;
    setLocalStorage(this.STORAGE_KEY, entries);
    
    return updatedEntry;
  }
  
  static async delete(id: string): Promise<void> {
    const entries = getLocalStorage<JournalEntry[]>(this.STORAGE_KEY, []);
    const filteredEntries = entries.filter(entry => entry.id !== id);
    setLocalStorage(this.STORAGE_KEY, filteredEntries);
  }
}

// Implementation of RomanticPlanService
export class RomanticPlanService {
  private static readonly STORAGE_KEY = 'betweenus_romantic_plans';

  static async filter(params: Partial<RomanticPlan> & { updated_after?: string } = {}): Promise<RomanticPlan[]> {
    const plans = getLocalStorage<RomanticPlan[]>(this.STORAGE_KEY, []);
    let filtered = filterObjects(plans, params);
    
    // Filter by updated_after if provided
    if (params.updated_after) {
      const afterDate = new Date(params.updated_after);
      filtered = filtered.filter(plan => new Date(plan.updated_date) > afterDate);
    }
    
    return filtered;
  }
  
  static async create(data: Omit<RomanticPlan, 'id'>): Promise<RomanticPlan> {
    const plans = getLocalStorage<RomanticPlan[]>(this.STORAGE_KEY, []);
    const newPlan: RomanticPlan = {
      ...data as RomanticPlan,
      id: generateId(),
      updated_date: new Date().toISOString()
    };
    
    plans.push(newPlan);
    setLocalStorage(this.STORAGE_KEY, plans);
    return newPlan;
  }
  
  static async update(id: string, updates: Partial<RomanticPlan>): Promise<RomanticPlan> {
    const plans = getLocalStorage<RomanticPlan[]>(this.STORAGE_KEY, []);
    const index = plans.findIndex(plan => plan.id === id);
    
    if (index === -1) {
      throw new Error(`Romantic plan with id ${id} not found`);
    }
    
    const updatedPlan = { 
      ...plans[index], 
      ...updates,
      updated_date: new Date().toISOString()
    };
    plans[index] = updatedPlan;
    setLocalStorage(this.STORAGE_KEY, plans);
    
    return updatedPlan;
  }
  
  static async delete(id: string): Promise<void> {
    const plans = getLocalStorage<RomanticPlan[]>(this.STORAGE_KEY, []);
    const filteredPlans = plans.filter(plan => plan.id !== id);
    setLocalStorage(this.STORAGE_KEY, filteredPlans);
  }
}

// Implementation of UserService
export class UserService {
  private static readonly STORAGE_KEY = 'betweenus_users';
  private static readonly CURRENT_USER_KEY = 'betweenus_current_user';

  static async me(): Promise<User> {
    const currentUser = getLocalStorage<User | null>(this.CURRENT_USER_KEY, null);
    
    if (currentUser) {
      return currentUser;
    }
    
    // Create a default user if none exists
    const defaultUser: User = {
      id: generateId(),
      email: `user_${generateId()}@example.com`,
      full_name: 'User',
      is_online: true,
      last_sync: new Date().toISOString()
    };
    
    setLocalStorage(this.CURRENT_USER_KEY, defaultUser);
    
    // Also add to users collection
    const users = getLocalStorage<User[]>(this.STORAGE_KEY, []);
    users.push(defaultUser);
    setLocalStorage(this.STORAGE_KEY, users);
    
    return defaultUser;
  }
  
  static async filter(params: Partial<User> = {}): Promise<User[]> {
    const users = getLocalStorage<User[]>(this.STORAGE_KEY, []);
    return filterObjects(users, params);
  }
  
  static async update(id: string, updates: Partial<User>): Promise<User> {
    const users = getLocalStorage<User[]>(this.STORAGE_KEY, []);
    const index = users.findIndex(user => user.id === id);
    
    if (index === -1) {
      throw new Error(`User with id ${id} not found`);
    }
    
    const updatedUser = { ...users[index], ...updates };
    users[index] = updatedUser;
    setLocalStorage(this.STORAGE_KEY, users);
    
    return updatedUser;
  }
  
  static async updateMyUserData(updates: Partial<User>): Promise<User> {
    const currentUser = await this.me();
    const updatedUser = { ...currentUser, ...updates };
    setLocalStorage(this.CURRENT_USER_KEY, updatedUser);
    
    // Also update in users collection
    const users = getLocalStorage<User[]>(this.STORAGE_KEY, []);
    const index = users.findIndex(user => user.id === currentUser.id);
    
    if (index !== -1) {
      users[index] = updatedUser;
      setLocalStorage(this.STORAGE_KEY, users);
    }
    
    return updatedUser;
  }
  
  static async delete(id: string): Promise<void> {
    const users = getLocalStorage<User[]>(this.STORAGE_KEY, []);
    const filteredUsers = users.filter(user => user.id !== id);
    setLocalStorage(this.STORAGE_KEY, filteredUsers);
    
    // If deleting current user, clear current user
    const currentUser = getLocalStorage<User | null>(this.CURRENT_USER_KEY, null);
    if (currentUser && currentUser.id === id) {
      localStorage.removeItem(this.CURRENT_USER_KEY);
    }
  }
}

// Implementation of NotificationService
export class NotificationService {
  private static readonly STORAGE_KEY = 'betweenus_notifications';

  static async filter(params: Partial<Notification> = {}, sortBy?: string): Promise<Notification[]> {
    const notifications = getLocalStorage<Notification[]>(this.STORAGE_KEY, []);
    const filtered = filterObjects(notifications, params);
    
    // Handle sorting
    if (sortBy) {
      const isDesc = sortBy.startsWith('-');
      const field = isDesc ? sortBy.substring(1) : sortBy;
      
      filtered.sort((a, b) => {
        if (a[field] < b[field]) return isDesc ? 1 : -1;
        if (a[field] > b[field]) return isDesc ? -1 : 1;
        return 0;
      });
    }
    
    return filtered;
  }
  
  static async create(data: Omit<Notification, 'id'>): Promise<Notification> {
    const notifications = getLocalStorage<Notification[]>(this.STORAGE_KEY, []);
    const newNotification: Notification = {
      ...data as Notification,
      id: generateId(),
      created_date: data.created_date || new Date().toISOString(),
      is_read: data.is_read || false
    };
    
    notifications.push(newNotification);
    setLocalStorage(this.STORAGE_KEY, notifications);
    return newNotification;
  }
  
  static async update(id: string, updates: Partial<Notification>): Promise<Notification> {
    const notifications = getLocalStorage<Notification[]>(this.STORAGE_KEY, []);
    const index = notifications.findIndex(notification => notification.id === id);
    
    if (index === -1) {
      throw new Error(`Notification with id ${id} not found`);
    }
    
    const updatedNotification = { ...notifications[index], ...updates };
    notifications[index] = updatedNotification;
    setLocalStorage(this.STORAGE_KEY, notifications);
    
    return updatedNotification;
  }
  
  static async delete(id: string): Promise<void> {
    const notifications = getLocalStorage<Notification[]>(this.STORAGE_KEY, []);
    const filteredNotifications = notifications.filter(notification => notification.id !== id);
    setLocalStorage(this.STORAGE_KEY, filteredNotifications);
  }
}

// Implementation of RoomService
export class RoomService {
  private static readonly STORAGE_KEY = 'betweenus_rooms';

  static async filter(params: Partial<Room> = {}): Promise<Room[]> {
    const rooms = getLocalStorage<Room[]>(this.STORAGE_KEY, []);
    return filterObjects(rooms, params);
  }
  
  static async create(data: Omit<Room, 'id'>): Promise<Room> {
    const rooms = getLocalStorage<Room[]>(this.STORAGE_KEY, []);
    const newRoom: Room = {
      ...data as Room,
      id: generateId()
    };
    
    rooms.push(newRoom);
    setLocalStorage(this.STORAGE_KEY, rooms);
    return newRoom;
  }
  
  static async update(id: string, updates: Partial<Room>): Promise<Room> {
    const rooms = getLocalStorage<Room[]>(this.STORAGE_KEY, []);
    const index = rooms.findIndex(room => room.id === id);
    
    if (index === -1) {
      throw new Error(`Room with id ${id} not found`);
    }
    
    const updatedRoom = { ...rooms[index], ...updates };
    rooms[index] = updatedRoom;
    setLocalStorage(this.STORAGE_KEY, rooms);
    
    return updatedRoom;
  }
}
