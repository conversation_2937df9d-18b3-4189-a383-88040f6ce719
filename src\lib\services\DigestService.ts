import { JournalEntry, RomanticPlan, User, NotificationService } from '@/src/lib/entities/all';
import { format, startOfWeek, endOfWeek } from 'date-fns';

/**
 * Service to handle weekly digest functionality
 */
export class DigestService {
  /**
   * Generate weekly digest data
   */
  static async generateWeeklyDigest(roomCode: string) {
    try {
      const [entries, plans] = await Promise.all([
        JournalEntry.filter({ room_code: roomCode, is_draft: false, is_secret: false }),
        RomanticPlan.filter({ room_code: roomCode })
      ]);
      
      const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
      const weekEnd = endOfWeek(new Date(), { weekStartsOn: 1 });
      
      const weeklyEntries = entries.filter(entry => {
        const entryDate = new Date(entry.created_date);
        return entryDate >= weekStart && entryDate <= weekEnd;
      });

      const weeklyPlans = plans.filter(plan => {
        const planDate = new Date(plan.created_date);
        return planDate >= weekStart && planDate <= weekEnd;
      });

      const praiseEntries = weeklyEntries.filter(e => ['praise', 'gratitude'].includes(e.entry_type));
      const issueEntries = weeklyEntries.filter(e => ['minor_issue', 'habitual', 'emotional_hurt', 'needs_discussion'].includes(e.entry_type));
      const resolvedIssues = issueEntries.filter(e => e.is_resolved);
      
      const praiseRatio = issueEntries.length > 0 ? (praiseEntries.length / issueEntries.length).toFixed(1) : praiseEntries.length;
      const resolutionRate = issueEntries.length > 0 ? Math.round((resolvedIssues.length / issueEntries.length) * 100) : 100;

      const allTags = weeklyEntries.flatMap(e => e.tags || []);
      const tagCounts = allTags.reduce((acc, tag) => {
        acc[tag] = (acc[tag] || 0) + 1;
        return acc;
      }, {});
      const topTags = Object.keys(tagCounts).length > 0 
        ? Object.entries(tagCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([tag]) => tag)
        : [];

      // Get mood data
      const moodData = this.analyzeMoodTrends(weeklyEntries);
      
      // Get growth insights
      const growthInsights = this.generateGrowthInsights(weeklyEntries, weeklyPlans);

      return {
        period: `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d')}`,
        totalEntries: weeklyEntries.length,
        praiseEntries: praiseEntries.length,
        issueEntries: issueEntries.length,
        praiseRatio,
        resolutionRate,
        topTags,
        weeklyPlans: weeklyPlans.length,
        highlights: praiseEntries.sort((a,b) => new Date(b.created_date) - new Date(a.created_date)).slice(0, 3),
        moodData,
        growthInsights,
        completedPlans: weeklyPlans.filter(p => p.status === 'completed').length,
        upcomingPlans: plans.filter(p => {
          const planDate = new Date(p.target_date);
          const nextWeekEnd = endOfWeek(new Date(weekEnd.getTime() + 86400000), { weekStartsOn: 1 });
          return planDate > weekEnd && planDate <= nextWeekEnd && p.status !== 'completed';
        })
      };
    } catch (error) {
      console.error('Error generating weekly digest:', error);
      throw error;
    }
  }

  /**
   * Analyze mood trends from journal entries
   */
  private static analyzeMoodTrends(entries: JournalEntry[]) {
    const moodCounts = entries.reduce((acc, entry) => {
      if (entry.mood) {
        acc[entry.mood] = (acc[entry.mood] || 0) + 1;
      }
      return acc;
    }, {});
    
    const totalEntries = entries.length;
    const moodPercentages = {};
    
    for (const [mood, count] of Object.entries(moodCounts)) {
      moodPercentages[mood] = Math.round((count / totalEntries) * 100);
    }
    
    const dominantMood = Object.entries(moodCounts).sort(([,a], [,b]) => b - a)[0]?.[0] || 'neutral';
    
    return {
      moodPercentages,
      dominantMood
    };
  }
  
  /**
   * Generate growth insights based on journal entries and plans
   */
  private static generateGrowthInsights(entries: JournalEntry[], plans: RomanticPlan[]) {
    const insights = [];
    
    // Check praise to issue ratio
    const praiseEntries = entries.filter(e => ['praise', 'gratitude'].includes(e.entry_type));
    const issueEntries = entries.filter(e => ['minor_issue', 'habitual', 'emotional_hurt', 'needs_discussion'].includes(e.entry_type));
    
    if (praiseEntries.length > issueEntries.length * 2) {
      insights.push("You're doing great at appreciating each other! Your praise-to-issue ratio is very healthy.");
    } else if (issueEntries.length > praiseEntries.length * 2) {
      insights.push("Consider focusing more on appreciation. Try to notice and journal about positive moments more frequently.");
    }
    
    // Check plan completion rate
    const completedPlans = plans.filter(p => p.status === 'completed').length;
    const totalPlans = plans.length;
    
    if (totalPlans > 0) {
      const completionRate = (completedPlans / totalPlans) * 100;
      if (completionRate >= 75) {
        insights.push("You're excellent at following through on your plans together!");
      } else if (completionRate <= 25) {
        insights.push("Consider setting more achievable plans or breaking larger plans into smaller steps.");
      }
    }
    
    // Add a default insight if none were generated
    if (insights.length === 0) {
      insights.push("Keep journaling consistently to see more personalized insights next week.");
    }
    
    return insights;
  }
  
  /**
   * Send weekly digest notifications to users
   */
  static async sendDigestNotifications(roomCode: string) {
    try {
      const users = await User.filter({ room_code: roomCode });
      const digest = await this.generateWeeklyDigest(roomCode);
      
      for (const user of users) {
        if (user.notification_preferences?.email_digest) {
          await NotificationService.create({
            recipient_email: user.email,
            type: 'weekly_digest',
            title: 'Your Weekly Relationship Digest',
            message: `Your weekly relationship summary for ${digest.period} is now available.`,
            room_code: roomCode,
            related_type: 'digest',
            action_url: '/digest'
          });
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error sending digest notifications:', error);
      return false;
    }
  }
}