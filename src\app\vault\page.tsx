"use client";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { JournalEntry, User } from "@/lib/entities/all";
import { VaultService } from "@/lib/services/VaultService";
import { EntryCard } from "@/components/journal/EntryCard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Heart, Lock, Unlock, AlertTriangle } from "lucide-react";
import { VaultIcon } from "@/components/icons/VaultIcon";
import { useToast } from "@/components/ui/use-toast";

export default function Vault() {
  const [vaultEntries, setVaultEntries] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [unlockedEntries, setUnlockedEntries] = useState(new Set());
  const [currentUser, setCurrentUser] = useState(null);
  const { toast } = useToast();

  useEffect(() => {
    loadVaultEntries();
  }, []);

  const loadVaultEntries = async () => {
    setIsLoading(true);
    try {
      const user = await User.me();
      setCurrentUser(user);
      const roomCode = sessionStorage.getItem('roomCode');
      if (roomCode) {
        // Use VaultService to get vault entries
        const data = await VaultService.getVaultEntries(roomCode);
        setVaultEntries(data);
        
        // Check which entries the user has access to
        const accessibleEntryIds = [];
        for (const entry of data) {
          if (await VaultService.hasAccess(entry.id, user.id)) {
            accessibleEntryIds.push(entry.id);
          }
        }
        
        setUnlockedEntries(new Set(accessibleEntryIds));
      }
    } catch (error) {
      console.error("Error loading vault entries:", error);
    }
    setIsLoading(false);
  };

  const handleUpdate = async (entryId, updates) => {
    await JournalEntry.update(entryId, updates);
    loadVaultEntries();
  };

  const unlockEntry = async (entryId) => {
    try {
      if (currentUser) {
        await VaultService.requestAccess(entryId, currentUser.id);
        // For demo purposes, automatically approve the request
        await VaultService.approveAccess(entryId);
        setUnlockedEntries(prev => new Set([...prev, entryId]));
        toast({
          title: "Vault Entry Unlocked",
          description: "You now have access to this entry.",
          variant: "default",
        });
      }
    } catch (error) {
      console.error("Error unlocking entry:", error);
      toast({
        title: "Error",
        description: "Failed to unlock entry. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full animate-pulse mx-auto mb-4"></div>
          <p className="text-blue-600 font-medium">Accessing the vault...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 md:p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <VaultIcon className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-blue-900 mb-3">The Vault</h1>
          <p className="text-blue-600 max-w-2xl mx-auto">
            Sensitive entries that require mutual agreement to access. Handle with care and love.
          </p>
        </div>

        <Card className="card-premium border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <Heart className="w-5 h-5 text-pink-500" />
              Vault Guidelines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-blue-700">
              <p>• Entries here contain sensitive or deeply emotional content</p>
              <p>• Both partners should approach these with extra care and understanding</p>
              <p>• Take time to process before responding</p>
              <p>• Remember: you&apos;re on the same team</p>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-6">
          {vaultEntries.map((entry) => {
            const isUnlocked = unlockedEntries.has(entry.id);
            
            if (!isUnlocked) {
              return (
                <Card key={entry.id} className="card-premium border-blue-200">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Lock className="w-8 h-8 text-blue-600" />
                    </div>
                    <h3 className="font-semibold text-blue-900 mb-2">Locked Vault Entry</h3>
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <Badge variant="outline" className="text-xs text-blue-600">
                        {format(new Date(entry.created_date), 'MMM d, yyyy')}
                      </Badge>
                      {entry.entry_type === 'emotional_hurt' && (
                        <Badge className="bg-red-100 text-red-800 text-xs">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Emotional Content
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-blue-600 mb-4">
                      This entry requires mutual agreement to open. Approach with care.
                    </p>
                    <Button
                      onClick={() => unlockEntry(entry.id)}
                      variant="outline"
                      className="border-blue-300 text-blue-700 hover:bg-blue-50"
                    >
                      <Unlock className="w-4 h-4 mr-2" />
                      Request to Unlock
                    </Button>
                  </CardContent>
                </Card>
              );
            }

            return (
              <div key={entry.id} className="relative">
                <div className="absolute -top-2 -left-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center z-10">
                  <Unlock className="w-3 h-3 text-white" />
                </div>
                <EntryCard
                  entry={entry}
                  onUpdate={handleUpdate}
                  currentUser={currentUser}
                />
              </div>
            );
          })}

          {vaultEntries.length === 0 && (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <VaultIcon className="w-10 h-10 text-blue-500" />
              </div>
              <h3 className="text-xl font-semibold text-blue-900 mb-2">Vault is Empty</h3>
              <p className="text-blue-600">
                No sensitive entries stored yet. This is a safe space for deeper emotional content.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}