"use client";

import React, { useState, useEffect } from "react";
import { JournalEntry, User, JournalEntryService, UserService } from "@/src/lib/entities/all";
import { Button } from "@/src/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/src/components/ui/tabs";
import { Plus } from "lucide-react";
import EntryForm from "@/src/components/journal/EntryForm";
import EntryCard from "@/src/components/journal/EntryCard";

export default function Journal() {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [editingDraft, setEditingDraft] = useState<JournalEntry | null>(null);

  const urlParams = new URLSearchParams(window.location.search);
  const defaultType = urlParams.get('type');

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (defaultType) {
      setShowForm(true);
      setEditingDraft(null);
    }
  }, [defaultType]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const user = await UserService.me();
      setCurrentUser(user);
      const roomCode = sessionStorage.getItem('roomCode');
      if (roomCode) {
        const data = await JournalEntryService.filter({ room_code: roomCode });
        setEntries(data);
      }
    } catch (error) {
      console.error("Error loading data:", error);
    }
    setIsLoading(false);
  };

  const handleCreate = async (entryData: Partial<JournalEntry>) => {
    const created = await JournalEntryService.create(entryData);
    
    // Create notification for new entry
    const { NotificationService } = await import('@/src/lib/services/NotificationService');
    await NotificationService.createJournalEntryNotification(created, currentUser);
    
    setShowForm(false);
    setEditingDraft(null);
    await loadData();
    return created;
  };

  const handleUpdate = async (entryId: string, updates: Partial<JournalEntry>) => {
    await JournalEntryService.update(entryId, updates);
    await loadData();
  };

  const handleUpdateDraft = async (entryId: string, updates: Partial<JournalEntry>) => {
    await JournalEntryService.update(entryId, updates);
    setShowForm(false);
    setEditingDraft(null);
    await loadData();
  };

  const editDraft = (draft: JournalEntry) => {
    setEditingDraft(draft);
    setShowForm(true);
  };

  const publicEntries = entries.filter(e => !e.is_draft && !e.is_secret && !e.is_vault);
  
  const filteredEntries = publicEntries.filter(entry => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'issues') {
      return ['minor_issue', 'habitual', 'emotional_hurt', 'needs_discussion'].includes(entry.entry_type);
    }
    if (activeFilter === 'positive') {
      return ['praise', 'gratitude'].includes(entry.entry_type);
    }
    if (activeFilter === 'unresolved') {
      return ['minor_issue', 'habitual', 'emotional_hurt', 'needs_discussion'].includes(entry.entry_type) && !entry.is_resolved;
    }
    return false;
  });

  const myDrafts = entries.filter(e => e.is_draft && e.created_by === currentUser?.email);
  const displayEntries = activeFilter === 'drafts' ? myDrafts : filteredEntries;

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gradient-to-br from-purple-50/30 via-pink-50/30 to-rose-50/30">
        <div className="text-center animate-fade-in-scale">
          <div className="w-20 h-20 bg-gradient-romantic rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-romantic animate-pulse-romantic">
            <Heart className="w-10 h-10 text-white" />
          </div>
          <div className="spinner-romantic mx-auto mb-4"></div>
          <p className="text-gray-600 font-semibold text-lg">Loading your journal...</p>
          <p className="text-gray-500 text-sm mt-2">Preparing your intimate space</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gradient-to-br from-purple-50/20 via-pink-50/20 to-rose-50/20 relative">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 right-10 w-48 h-48 bg-gradient-to-br from-purple-200/15 to-pink-200/15 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-10 left-10 w-64 h-64 bg-gradient-to-tr from-pink-200/15 to-purple-200/15 rounded-full blur-2xl animate-float" style={{animationDelay: '1.5s'}}></div>
      </div>

      <div className="container-romantic space-y-8 h-full flex flex-col relative z-10">
        {/* Enhanced Header */}
        <div className="text-center pt-6 animate-fade-in-up">
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-romantic rounded-2xl flex items-center justify-center shadow-romantic animate-pulse-romantic">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-display font-bold gradient-text">Live Journal</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Share your thoughts, feelings, and experiences in real-time with your partner in this sacred space of connection.
          </p>
        </div>

        {/* Enhanced Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-6 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
          {/* Enhanced Mobile-friendly tabs */}
          <div className="w-full sm:w-auto overflow-x-auto scrollbar-romantic">
            <div className="border-b-2 border-romantic-200/30">
              <Tabs value={activeFilter} onValueChange={setActiveFilter} className="w-full">
                <TabsList className="bg-white/60 backdrop-blur-sm rounded-xl p-1 shadow-soft border border-romantic-200/50">
                  <TabsTrigger value="all" className="whitespace-nowrap px-4 py-2.5 text-sm font-semibold rounded-lg data-[state=active]:bg-gradient-romantic data-[state=active]:text-white data-[state=active]:shadow-romantic transition-all duration-300">All</TabsTrigger>
                  <TabsTrigger value="positive" className="whitespace-nowrap px-4 py-2.5 text-sm font-semibold rounded-lg data-[state=active]:bg-gradient-romantic data-[state=active]:text-white data-[state=active]:shadow-romantic transition-all duration-300">Positive</TabsTrigger>
                  <TabsTrigger value="issues" className="whitespace-nowrap px-4 py-2.5 text-sm font-semibold rounded-lg data-[state=active]:bg-gradient-romantic data-[state=active]:text-white data-[state=active]:shadow-romantic transition-all duration-300">Issues</TabsTrigger>
                  <TabsTrigger value="unresolved" className="whitespace-nowrap px-4 py-2.5 text-sm font-semibold rounded-lg data-[state=active]:bg-gradient-romantic data-[state=active]:text-white data-[state=active]:shadow-romantic transition-all duration-300">Unresolved</TabsTrigger>
                  <TabsTrigger value="drafts" className="whitespace-nowrap px-3 py-2 text-sm">Drafts</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          <Button
            onClick={() => { setShowForm(!showForm); setEditingDraft(null); }}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 w-full sm:w-auto shrink-0"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Entry
          </Button>
        </div>

        {/* Entry Form */}
        {showForm && (
          <EntryForm
            onSubmit={handleCreate}
            onCancel={() => { setShowForm(false); setEditingDraft(null); }}
            existingDraft={editingDraft}
            onUpdateDraft={handleUpdateDraft}
          />
        )}

        {/* Entries List */}
        <div className="space-y-6 flex-1 overflow-y-auto">
          {displayEntries.map((entry) => (
            <div key={entry.id} className="relative">
              {entry.is_draft && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="absolute top-4 right-4 z-10 bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
                  onClick={() => editDraft(entry)}
                >
                  Edit Draft
                </Button>
              )}
              <EntryCard
                entry={entry}
                onUpdate={handleUpdate}
                currentUser={currentUser}
                showResponse={!entry.is_draft}
              />
            </div>
          ))}

          {displayEntries.length === 0 && (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">📝</span>
              </div>
              <h3 className="text-xl font-semibold text-purple-900 mb-2">No entries yet for this view</h3>
              <p className="text-purple-600 mb-4">Start sharing your thoughts and feelings with each other.</p>
              <Button
                onClick={() => {setShowForm(true); setEditingDraft(null);}}
                className="bg-gradient-to-r from-purple-500 to-pink-500"
              >
                Write First Entry
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
