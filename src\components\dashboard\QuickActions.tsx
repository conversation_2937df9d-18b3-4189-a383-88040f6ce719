import React from 'react';
import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/src/components/ui/card";
import { But<PERSON> } from "@/src/components/ui/button";
import { Plus, Heart, Sparkles, Vault } from "lucide-react";

export default function QuickActions() {
  const actions = [
    {
      title: "Write in Journal",
      description: "Share what's on your mind",
      icon: Plus,
      href: "/journal",
      gradient: "from-purple-500 to-purple-600",
      hoverGradient: "hover:from-purple-600 hover:to-purple-700"
    },
    {
      title: "Express Gratitude",
      description: "Appreciate your partner",
      icon: Heart,
      href: "/journal?type=gratitude",
      gradient: "from-pink-500 to-pink-600",
      hoverGradient: "hover:from-pink-600 hover:to-pink-700"
    },
    {
      title: "Open Vault",
      description: "Access sensitive topics",
      icon: Vault,
      href: "/vault",
      gradient: "from-blue-500 to-blue-600",
      hoverGradient: "hover:from-blue-600 hover:to-blue-700"
    },
    {
      title: "Plan Romance",
      description: "Create something special",
      icon: Sparkles,
      href: "/romance",
      gradient: "from-yellow-500 to-yellow-600",
      hoverGradient: "hover:from-yellow-600 hover:to-yellow-700"
    }
  ];

  return (
    <Card className="card-premium">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-purple-900">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {actions.map((action, index) => (
          <Link key={index} href={action.href}>
            <Button
              variant="ghost"
              className={`w-full justify-start gap-3 h-auto p-4 hover:bg-purple-50 ${action.hoverGradient}`}
            >
              <div className={`p-2 rounded-lg bg-gradient-to-r ${action.gradient} bg-opacity-20`}>
                <action.icon className="w-4 h-4 text-purple-700" />
              </div>
              <div className="text-left">
                <p className="font-medium text-purple-900">{action.title}</p>
                <p className="text-xs text-purple-600">{action.description}</p>
              </div>
            </Button>
          </Link>
        ))}
      </CardContent>
    </Card>
  );
}