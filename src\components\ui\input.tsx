import * as React from "react"

import { cn } from "@/src/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "flex h-12 w-full min-w-0 rounded-xl border-2 border-romantic-200/50 bg-white/80 backdrop-blur-sm px-4 py-3 text-base shadow-soft transition-all duration-300 outline-none",
        "file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-700",
        "placeholder:text-gray-500 selection:bg-romantic-200 selection:text-romantic-800",
        "focus-visible:border-romantic-400 focus-visible:ring-2 focus-visible:ring-romantic-400/50 focus-visible:ring-offset-2 focus-visible:bg-white/95",
        "hover:border-romantic-300/70 hover:bg-white/90",
        "disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
        "aria-invalid:ring-red-400/20 aria-invalid:border-red-400",
        "md:text-sm",
        className
      )}
      {...props}
    />
  )
}

export { Input }