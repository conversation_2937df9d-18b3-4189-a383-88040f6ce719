"use client";
import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { JournalEntry } from "../../lib/entities/all";
import { GrowthMetric, GrowthInsight } from "../../lib/services/GrowthService";
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { TrendingUp, Heart, AlertCircle, BarChart3, ArrowUp, ArrowDown, Minus } from "lucide-react";
import { useToast } from "../../components/ui/use-toast";

export default function Growth() {
  const router = useRouter();
  const { toast } = useToast();
  const [entries] = useState<JournalEntry[]>([]);
  const [metrics, setMetrics] = useState<GrowthMetric[]>([]);
  const [insights, setInsights] = useState<GrowthInsight[]>([]);
  const [timeframe, setTimeframe] = useState<'weekly' | 'monthly' | 'yearly'>('monthly');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, [timeframe, loadData]);

  const loadData = useCallback(async () => {
    setIsLoading(true);
    try {
      const roomCode = sessionStorage.getItem('roomCode');
      if (!roomCode) {
        router.push('/join');
        return;
      }

      // Use mock data for now since we're having import issues
      const mockMetrics = [
        {
          id: "communication_frequency",
          name: "Communication Frequency",
          value: 12,
          previousValue: 8,
          change: 50,
          trend: "up",
          description: "Number of meaningful conversations per week"
        },
        {
          id: "romantic_initiative",
          name: "Romantic Initiative",
          value: 3,
          previousValue: 2,
          change: 50,
          trend: "up",
          description: "Number of romantic gestures initiated"
        },
        {
          id: "positivity_ratio",
          name: "Positivity Ratio",
          value: 4.2,
          previousValue: 3.5,
          change: 20,
          trend: "up",
          description: "Ratio of positive to negative interactions"
        },
        {
          id: "resolution_rate",
          name: "Issue Resolution Rate",
          value: 85,
          previousValue: 70,
          change: 21.4,
          trend: "up",
          description: "Percentage of issues successfully resolved"
        }
      ];
      
      setMetrics(mockMetrics);
      
      const mockInsights = [
        {
          id: "communication_growth",
          title: "Communication is Improving",
          description: "Your communication frequency has increased by 50% compared to last month.",
          suggestion: "Try scheduling a weekly check-in to maintain this positive trend.",
          supportingMetrics: ["communication_frequency"]
        },
        {
          id: "romance_boost",
          title: "Romance is Flourishing",
          description: "You've both been more proactive with romantic gestures lately.",
          suggestion: "Consider creating a shared bucket list of new experiences to try together.",
          supportingMetrics: ["romantic_initiative"]
        }
      ];
      
      setInsights(mockInsights);
    } catch (error) {
      console.error('Error loading growth data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load growth data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [router, toast]);

  // Analytics calculations for backward compatibility
  const issueEntries = entries.filter(e => ['minor_issue', 'habitual', 'emotional_hurt', 'needs_discussion'].includes(e.entry_type));
  const praiseEntries = entries.filter(e => ['praise', 'gratitude'].includes(e.entry_type));
  const resolvedIssues = issueEntries.filter(e => e.is_resolved);
  
  const topTags = entries
    .flatMap(e => e.tags || [])
    .reduce((acc, tag) => {
      acc[tag] = (acc[tag] || 0) + 1;
      return acc;
    }, {});
    
  const sortedTags = Object.entries(topTags)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10);

  const moodDistribution = entries.reduce((acc, entry) => {
    if (entry.mood) {
      acc[entry.mood] = (acc[entry.mood] || 0) + 1;
    }
    return acc;
  }, {});

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50/30 via-emerald-50/30 to-teal-50/30">
        <div className="text-center animate-fade-in-scale">
          <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-romantic animate-pulse-romantic">
            <TrendingUp className="w-12 h-12 text-white" />
          </div>
          <div className="spinner-romantic mx-auto mb-4"></div>
          <p className="text-gray-600 font-semibold text-lg">Analyzing your growth...</p>
          <p className="text-gray-500 text-sm mt-2">Discovering beautiful patterns</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 md:p-8 overflow-x-hidden bg-gradient-to-br from-green-50/20 via-emerald-50/20 to-teal-50/20 relative">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-green-200/20 to-emerald-200/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tr from-emerald-200/20 to-teal-200/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="container-romantic space-y-10 relative z-10">
        {/* Enhanced Header */}
        <div className="text-center animate-fade-in-up">
          <div className="inline-flex items-center gap-4 mb-6">
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-3xl flex items-center justify-center shadow-romantic animate-pulse-romantic">
              <TrendingUp className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-display font-bold gradient-text">Growth Tracker</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Track your relationship patterns, celebrate progress, and identify areas for growth together on this beautiful journey.
          </p>
        </div>

        {/* Timeframe Selector */}
        <div className="flex justify-center gap-4 mb-6">
          <Badge 
            className={`px-4 py-2 cursor-pointer ${timeframe === 'week' ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            onClick={() => setTimeframe('week')}
          >
            Weekly
          </Badge>
          <Badge 
            className={`px-4 py-2 cursor-pointer ${timeframe === 'month' ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            onClick={() => setTimeframe('month')}
          >
            Monthly
          </Badge>
          <Badge 
            className={`px-4 py-2 cursor-pointer ${timeframe === 'year' ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            onClick={() => setTimeframe('year')}
          >
            Yearly
          </Badge>
        </div>
        
        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {metrics.map((metric) => (
            <Card key={metric.id} className="card-premium">
              <CardContent className="p-4 sm:p-6">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-green-900 text-sm">{metric.name}</h3>
                  <Badge 
                    className={`
                      ${metric.trend === 'up' ? 'bg-green-100 text-green-800' : 
                        metric.trend === 'down' ? 'bg-red-100 text-red-800' : 
                        'bg-gray-100 text-gray-800'}
                    `}
                  >
                    {metric.change > 0 ? '+' : ''}{metric.change}%
                    {metric.trend === 'up' && <ArrowUp className="w-3 h-3 ml-1" />}
                    {metric.trend === 'down' && <ArrowDown className="w-3 h-3 ml-1" />}
                    {metric.trend === 'stable' && <Minus className="w-3 h-3 ml-1" />}
                  </Badge>
                </div>
                <div className="text-center mt-4">
                  <div className="text-3xl font-bold text-green-700">
                    {metric.id === 'positivity_ratio' || metric.id === 'resolution_rate' 
                      ? `${metric.value}${metric.id === 'resolution_rate' ? '%' : ''}`
                      : metric.value
                    }
                  </div>
                  <p className="text-xs text-green-600 mt-1">{metric.description}</p>
                  <div className="mt-2 text-xs text-gray-500">
                    Previous: {metric.previousValue}{metric.id === 'resolution_rate' ? '%' : ''}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {metrics.length === 0 && (
            <>
              <Card className="card-premium">
                <CardContent className="p-4 sm:p-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <BarChart3 className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-blue-900 text-lg">{entries.length}</h3>
                  <p className="text-sm text-blue-600">Total Entries</p>
                </CardContent>
              </Card>

              <Card className="card-premium">
                <CardContent className="p-4 sm:p-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-pink-900 text-lg">{praiseEntries.length}</h3>
                  <p className="text-sm text-pink-600">Positive Entries</p>
                </CardContent>
              </Card>

              <Card className="card-premium">
                <CardContent className="p-4 sm:p-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <AlertCircle className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-orange-900 text-lg">{issueEntries.length}</h3>
                  <p className="text-sm text-orange-600">Issues Raised</p>
                </CardContent>
              </Card>

              <Card className="card-premium">
                <CardContent className="p-4 sm:p-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-green-900 text-lg">
                    {issueEntries.length > 0 ? Math.round((resolvedIssues.length / issueEntries.length) * 100) : 0}%
                  </h3>
                  <p className="text-sm text-green-600">Resolution Rate</p>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        <div className="grid lg:grid-cols-2 gap-6 sm:gap-8">
          {/* Top Tags */}
          <Card className="card-premium">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-purple-900">Most Common Topics</CardTitle>
              <p className="text-sm text-purple-600">What you discuss most often</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {sortedTags.map(([tag, count]) => (
                  <div key={tag} className="flex items-center justify-between">
                    <Badge variant="outline" className="bg-purple-50 text-purple-700 truncate max-w-[60%]">
                      #{tag}
                    </Badge>
                    <div className="flex items-center gap-2">
                      <div className="w-16 sm:w-20 h-2 bg-purple-100 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-purple-400 to-purple-600 progress-fill"
                          style={{ width: `${(count / Math.max(...sortedTags.map(([,c]) => c))) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium text-purple-800 shrink-0">{count}</span>
                    </div>
                  </div>
                ))}
                {sortedTags.length === 0 && (
                  <p className="text-center text-purple-500 py-4">No tags used yet</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Mood Distribution */}
          <Card className="card-premium">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-purple-900">Mood Patterns</CardTitle>
              <p className="text-sm text-purple-600">How you&apos;ve been feeling</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(moodDistribution).map(([mood, count]) => {
                  const moodEmojis = {
                    very_happy: '😊',
                    happy: '🙂',
                    neutral: '😐',
                    sad: '😔',
                    frustrated: '😤',
                    angry: '😠'
                  };
                  
                  return (
                    <div key={mood} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg shrink-0">{moodEmojis[mood]}</span>
                        <span className="text-sm font-medium text-purple-800 capitalize truncate">
                          {mood.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-16 sm:w-20 h-2 bg-purple-100 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-gradient-to-r from-purple-400 to-purple-600 progress-fill"
                            style={{ width: `${entries.length > 0 ? (count / entries.length) * 100 : 0}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium text-purple-800 shrink-0">{count}</span>
                      </div>
                    </div>
                  );
                })}
                {Object.keys(moodDistribution).length === 0 && (
                  <p className="text-center text-purple-500 py-4">No mood data yet</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Insights */}
        <Card className="card-premium">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-purple-900">Growth Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              {insights.length > 0 ? (
                insights.map((insight, index) => (
                  <div key={index} className="space-y-3">
                    <div className={`p-4 ${insight.type === 'strength' ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'} rounded-lg border`}>
                      <h4 className={`font-medium ${insight.type === 'strength' ? 'text-green-800' : 'text-blue-800'} mb-2`}>
                        {insight.type === 'strength' ? '✨ ' : '🎯 '}{insight.title}
                      </h4>
                      <p className={`text-sm ${insight.type === 'strength' ? 'text-green-700' : 'text-blue-700'}`}>
                        {insight.description}
                      </p>
                      {insight.suggestion && (
                        <div className="mt-3 text-sm font-medium">
                          Suggestion: {insight.suggestion}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <>
                  <div className="space-y-3">
                    <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                      <h4 className="font-medium text-green-800 mb-2">✨ Positive Patterns</h4>
                      <ul className="text-sm text-green-700 space-y-1">
                        <li>• High praise-to-issue ratio shows healthy communication</li>
                        <li>• Regular journaling builds emotional intimacy</li>
                        <li>• Resolved issues demonstrate problem-solving skills</li>
                      </ul>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-medium text-blue-800 mb-2">🎯 Growth Opportunities</h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Consider discussing recurring themes more deeply</li>
                        <li>• Balance emotional entries with celebration</li>
                        <li>• Set regular check-ins for unresolved items</li>
                      </ul>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}