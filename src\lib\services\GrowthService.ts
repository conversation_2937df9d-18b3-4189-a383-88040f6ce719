import { JournalEntry, RomanticPlan } from "../entities/all";

export interface GrowthMetric {
  id: string;
  name: string;
  value: number;
  previousValue: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  description: string;
}

export interface GrowthInsight {
  id: string;
  title: string;
  description: string;
  type: 'positive' | 'negative' | 'neutral';
  date: string;
}

export class GrowthService {
  /**
   * Calculate growth metrics based on journal entries and romantic plans
   */
  static async calculateMetrics(roomCode: string, timeframe: 'week' | 'month' | 'year' = 'month'): Promise<GrowthMetric[]> {
    const metrics: GrowthMetric[] = [];
    
    // Get journal entries and romantic plans
    const entries = await JournalEntry.filter({ room_code: roomCode });
    const plans = await RomanticPlan.filter({ room_code: roomCode });
    
    // Calculate date ranges
    const now = new Date();
    const currentPeriodStart = this.getTimeframeDateStart(now, timeframe);
    const previousPeriodStart = this.getTimeframeDateStart(currentPeriodStart, timeframe, -1);
    
    // Filter entries and plans by date
    const currentEntries = entries.filter(entry => new Date(entry.created_date) >= currentPeriodStart);
    const previousEntries = entries.filter(entry => 
      new Date(entry.created_date) >= previousPeriodStart && 
      new Date(entry.created_date) < currentPeriodStart
    );
    
    const currentPlans = plans.filter(plan => new Date(plan.created_date) >= currentPeriodStart);
    const previousPlans = plans.filter(plan => 
      new Date(plan.created_date) >= previousPeriodStart && 
      new Date(plan.created_date) < currentPeriodStart
    );
    
    // Calculate metrics
    
    // 1. Communication Frequency
    const currentCommunicationFreq = currentEntries.length;
    const previousCommunicationFreq = previousEntries.length;
    const communicationChange = this.calculatePercentChange(previousCommunicationFreq, currentCommunicationFreq);
    
    metrics.push({
      id: 'communication_frequency',
      name: 'Communication Frequency',
      value: currentCommunicationFreq,
      previousValue: previousCommunicationFreq,
      change: communicationChange,
      trend: this.determineTrend(communicationChange),
      description: `Number of journal entries in the ${timeframe}`
    });
    
    // 2. Romantic Initiative
    const currentRomanticInit = currentPlans.length;
    const previousRomanticInit = previousPlans.length;
    const romanticChange = this.calculatePercentChange(previousRomanticInit, currentRomanticInit);
    
    metrics.push({
      id: 'romantic_initiative',
      name: 'Romantic Initiative',
      value: currentRomanticInit,
      previousValue: previousRomanticInit,
      change: romanticChange,
      trend: this.determineTrend(romanticChange),
      description: `Number of romantic plans created in the ${timeframe}`
    });
    
    // 3. Positivity Ratio
    const currentPositiveEntries = currentEntries.filter(entry => 
      entry.entry_type === 'gratitude' || 
      entry.entry_type === 'praise'
    ).length;
    
    const previousPositiveEntries = previousEntries.filter(entry => 
      entry.entry_type === 'gratitude' || 
      entry.entry_type === 'praise'
    ).length;
    
    const currentNegativeEntries = currentEntries.filter(entry => 
      entry.entry_type === 'issue' || 
      entry.entry_type === 'emotional_hurt'
    ).length;
    
    const previousNegativeEntries = previousEntries.filter(entry => 
      entry.entry_type === 'issue' || 
      entry.entry_type === 'emotional_hurt'
    ).length;
    
    const currentPositivityRatio = currentNegativeEntries > 0 
      ? parseFloat((currentPositiveEntries / currentNegativeEntries).toFixed(1))
      : currentPositiveEntries > 0 ? 5 : 0;
      
    const previousPositivityRatio = previousNegativeEntries > 0 
      ? parseFloat((previousPositiveEntries / previousNegativeEntries).toFixed(1))
      : previousPositiveEntries > 0 ? 5 : 0;
      
    const positivityChange = this.calculatePercentChange(previousPositivityRatio, currentPositivityRatio);
    
    metrics.push({
      id: 'positivity_ratio',
      name: 'Positivity Ratio',
      value: currentPositivityRatio,
      previousValue: previousPositivityRatio,
      change: positivityChange,
      trend: this.determineTrend(positivityChange),
      description: `Ratio of positive to negative entries (higher is better)`
    });
    
    // 4. Resolution Rate
    const currentIssues = currentEntries.filter(entry => entry.entry_type === 'issue').length;
    const currentResolved = currentEntries.filter(entry => 
      entry.entry_type === 'issue' && entry.is_resolved
    ).length;
    
    const previousIssues = previousEntries.filter(entry => entry.entry_type === 'issue').length;
    const previousResolved = previousEntries.filter(entry => 
      entry.entry_type === 'issue' && entry.is_resolved
    ).length;
    
    const currentResolutionRate = currentIssues > 0 
      ? parseFloat(((currentResolved / currentIssues) * 100).toFixed(1))
      : 100;
      
    const previousResolutionRate = previousIssues > 0 
      ? parseFloat(((previousResolved / previousIssues) * 100).toFixed(1))
      : 100;
      
    const resolutionChange = this.calculatePercentChange(previousResolutionRate, currentResolutionRate);
    
    metrics.push({
      id: 'resolution_rate',
      name: 'Resolution Rate',
      value: currentResolutionRate,
      previousValue: previousResolutionRate,
      change: resolutionChange,
      trend: this.determineTrend(resolutionChange),
      description: `Percentage of issues that were resolved`
    });
    
    return metrics;
  }
  
  /**
   * Generate insights based on growth metrics
   */
  static async generateInsights(roomCode: string): Promise<GrowthInsight[]> {
    const metrics = await this.calculateMetrics(roomCode);
    const insights: GrowthInsight[] = [];
    
    // Generate insights based on metrics
    metrics.forEach(metric => {
      if (Math.abs(metric.change) >= 10) {
        const type = metric.trend === 'up' ? 'positive' : 'negative';
        const direction = metric.trend === 'up' ? 'increased' : 'decreased';
        
        // Skip if the metric is resolution rate and trend is down (that's negative)
        if (metric.id === 'resolution_rate' && metric.trend === 'down') {
          insights.push({
            id: `${metric.id}_${Date.now()}`,
            title: `Resolution Rate Declining`,
            description: `Your issue resolution rate has decreased by ${Math.abs(metric.change).toFixed(0)}%. Consider focusing on addressing unresolved issues.`,
            type: 'negative',
            date: new Date().toISOString()
          });
        } 
        // Skip if the metric is positivity ratio and trend is down (that's negative)
        else if (metric.id === 'positivity_ratio' && metric.trend === 'down') {
          insights.push({
            id: `${metric.id}_${Date.now()}`,
            title: `Positivity Declining`,
            description: `Your positivity ratio has decreased by ${Math.abs(metric.change).toFixed(0)}%. Try focusing on more gratitude and praise entries.`,
            type: 'negative',
            date: new Date().toISOString()
          });
        }
        // For other metrics, use the trend direction
        else {
          insights.push({
            id: `${metric.id}_${Date.now()}`,
            title: `${metric.name} ${direction.charAt(0).toUpperCase() + direction.slice(1)}`,
            description: `Your ${metric.name.toLowerCase()} has ${direction} by ${Math.abs(metric.change).toFixed(0)}% compared to the previous period.`,
            type: type,
            date: new Date().toISOString()
          });
        }
      }
    });
    
    // Add general insights if we don't have enough
    if (insights.length < 2) {
      insights.push({
        id: `general_${Date.now()}`,
        title: 'Keep Building Your Relationship',
        description: 'Continue journaling and planning romantic activities to see more detailed growth insights.',
        type: 'neutral',
        date: new Date().toISOString()
      });
    }
    
    return insights;
  }
  
  /**
   * Helper method to get the start date for a timeframe
   */
  private static getTimeframeDateStart(date: Date, timeframe: 'week' | 'month' | 'year', offset = 0): Date {
    const result = new Date(date);
    
    switch (timeframe) {
      case 'week':
        result.setDate(result.getDate() - result.getDay() + (offset * 7));
        break;
      case 'month':
        result.setDate(1);
        result.setMonth(result.getMonth() + offset);
        break;
      case 'year':
        result.setMonth(0);
        result.setDate(1);
        result.setFullYear(result.getFullYear() + offset);
        break;
    }
    
    return result;
  }
  
  /**
   * Helper method to calculate percent change
   */
  private static calculatePercentChange(previous: number, current: number): number {
    if (previous === 0) {
      return current > 0 ? 100 : 0;
    }
    
    return parseFloat((((current - previous) / previous) * 100).toFixed(1));
  }
  
  /**
   * Helper method to determine trend direction
   */
  private static determineTrend(change: number): 'up' | 'down' | 'stable' {
    if (change > 5) return 'up';
    if (change < -5) return 'down';
    return 'stable';
  }
}