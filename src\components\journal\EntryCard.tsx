import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/src/components/ui/card";
import { But<PERSON> } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { Textarea } from "@/src/components/ui/textarea";
import { format } from 'date-fns';
import { Heart, MessageSquare, AlertTriangle, Sparkles, Reply, Check, X } from "lucide-react";

const entryTypeIcons = {
  praise: Heart,
  gratitude: Heart,
  minor_issue: AlertTriangle,
  habitual: AlertTriangle,
  emotional_hurt: AlertTriangle,
  needs_discussion: AlertTriangle,
  general: MessageSquare,
  romantic: Sparkles
};

const entryTypeColors = {
  praise: "bg-green-100 text-green-800 border-green-200",
  gratitude: "bg-pink-100 text-pink-800 border-pink-200",
  minor_issue: "bg-yellow-100 text-yellow-800 border-yellow-200",
  habitual: "bg-orange-100 text-orange-800 border-orange-200",
  emotional_hurt: "bg-red-100 text-red-800 border-red-200",
  needs_discussion: "bg-blue-100 text-blue-800 border-blue-200",
  general: "bg-gray-100 text-gray-800 border-gray-200",
  romantic: "bg-purple-100 text-purple-800 border-purple-200"
};

const moodEmojis = {
  very_happy: '😊',
  happy: '🙂',
  neutral: '😐',
  sad: '😔',
  frustrated: '😤',
  angry: '😠'
};

export default function EntryCard({ entry, onUpdate, currentUser, showResponse = true }) {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyText, setReplyText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const Icon = entryTypeIcons[entry.entry_type] || MessageSquare;
  const isAuthor = entry.author_email === currentUser?.email;
  const isIssueType = ['minor_issue', 'habitual', 'emotional_hurt', 'needs_discussion'].includes(entry.entry_type);

  const handleReply = async () => {
    if (!replyText.trim()) return;

    setIsSubmitting(true);
    try {
      const updates = {
        partner_response: replyText,
        response_date: new Date().toISOString()
      };
      await onUpdate(entry.id, updates);
      setReplyText('');
      setShowReplyForm(false);
    } catch (error) {
      console.error('Error submitting reply:', error);
    }
    setIsSubmitting(false);
  };

  const handleResolve = async () => {
    try {
      await onUpdate(entry.id, { is_resolved: true, resolved_date: new Date().toISOString() });
    } catch (error) {
      console.error('Error resolving entry:', error);
    }
  };

  const handleUnresolve = async () => {
    try {
      await onUpdate(entry.id, { is_resolved: false, resolved_date: null });
    } catch (error) {
      console.error('Error unresolving entry:', error);
    }
  };

  return (
    <Card className={`card-premium transition-all duration-300 ${entry.is_draft ? 'border-yellow-200 bg-yellow-50/50' : ''}`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-full">
              <Icon className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <span className="font-semibold text-purple-900">
                  {isAuthor ? 'You' : 'Your Partner'}
                </span>
                <Badge className={`text-xs ${entryTypeColors[entry.entry_type] || entryTypeColors.general}`}>
                  {entry.entry_type?.replace('_', ' ')}
                </Badge>
                {entry.mood && (
                  <span className="text-lg" title={entry.mood.replace('_', ' ')}>
                    {moodEmojis[entry.mood]}
                  </span>
                )}
              </div>
              <p className="text-sm text-purple-600">
                {format(new Date(entry.created_date), 'MMM d, yyyy • h:mm a')}
              </p>
            </div>
          </div>
          
          {entry.is_draft && (
            <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
              Draft
            </Badge>
          )}
        </div>
        
        {entry.title && (
          <h3 className="text-lg font-semibold text-purple-900 mt-2">{entry.title}</h3>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="prose prose-purple max-w-none">
          <p className="text-purple-800 whitespace-pre-wrap">{entry.content}</p>
        </div>

        {/* Resolution Status for Issues */}
        {isIssueType && (
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm font-medium text-gray-700">
              Status: {entry.is_resolved ? 'Resolved' : 'Open'}
            </span>
            {showResponse && (
              <Button
                size="sm"
                variant="outline"
                onClick={entry.is_resolved ? handleUnresolve : handleResolve}
                className={entry.is_resolved ? 'text-orange-600 hover:text-orange-700' : 'text-green-600 hover:text-green-700'}
              >
                {entry.is_resolved ? (
                  <>
                    <X className="w-4 h-4 mr-1" />
                    Reopen
                  </>
                ) : (
                  <>
                    <Check className="w-4 h-4 mr-1" />
                    Mark Resolved
                  </>
                )}
              </Button>
            )}
          </div>
        )}

        {/* Partner Response */}
        {entry.partner_response && (
          <div className="border-l-4 border-pink-300 pl-4 py-2 bg-pink-50 rounded-r-lg">
            <div className="flex items-center gap-2 mb-2">
              <Reply className="w-4 h-4 text-pink-600" />
              <span className="font-medium text-pink-800">
                {isAuthor ? 'Your Partner' : 'You'} responded:
              </span>
              {entry.response_date && (
                <span className="text-xs text-pink-600">
                  {format(new Date(entry.response_date), 'MMM d • h:mm a')}
                </span>
              )}
            </div>
            <p className="text-pink-800 whitespace-pre-wrap">{entry.partner_response}</p>
          </div>
        )}

        {/* Reply Form */}
        {showResponse && !isAuthor && !entry.partner_response && !entry.is_draft && (
          <div className="space-y-3">
            {!showReplyForm ? (
              <Button
                variant="outline"
                onClick={() => setShowReplyForm(true)}
                className="w-full text-purple-600 border-purple-200 hover:bg-purple-50"
              >
                <Reply className="w-4 h-4 mr-2" />
                Respond
              </Button>
            ) : (
              <div className="space-y-3">
                <Textarea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="Share your thoughts on this..."
                  className="min-h-[80px]"
                />
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowReplyForm(false);
                      setReplyText('');
                    }}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleReply}
                    disabled={!replyText.trim() || isSubmitting}
                    className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Response'}
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}