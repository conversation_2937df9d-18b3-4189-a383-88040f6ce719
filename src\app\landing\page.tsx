"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/src/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { Heart, ArrowRight, ArrowLeft, Eye, EyeOff, CheckCircle, Clock } from "lucide-react";
import Link from "next/link";
import { UserService, NotificationService, RoomService } from "@/src/lib/entities/all";

export default function LandingPage() {
  const [step, setStep] = useState('room');
  const [roomCode, setRoomCode] = useState('');
  const [pin, setPin] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [, setRoomExists] = useState(false);
  const [roomCreator, setRoomCreator] = useState(null);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showJoinDialog, setShowJoinDialog] = useState(false);
  const [joinRequestName, setJoinRequestName] = useState('');

  const handleRoomVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!roomCode.trim()) {
      setError("Please enter a room code.");
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const rooms = await RoomService.filter({ room_code: roomCode.trim() });
      
      if (rooms.length > 0) {
        const room = rooms[0];
        setRoomExists(true);
        setRoomCreator({ email: room.creator_email });
        setStep('pin');
      } else {
        setRoomExists(false);
        setStep('setup');
      }
    } catch {
      setError("Unable to verify room code. Please try again.");
    }
    
    setIsLoading(false);
  };

  const handlePinLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!pin.trim() || pin.length !== 8) {
      setError("Please enter your 8-digit pin.");
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const currentUser = await UserService.me();
      const usersInRoom = await UserService.filter({ room_code: roomCode.trim(), user_pin: pin });
      const existingUser = usersInRoom.find(u => u.email === currentUser.email);

      if (existingUser) {
        sessionStorage.setItem('roomCode', roomCode.trim());
        sessionStorage.setItem('userPin', pin);
        
        await UserService.updateMyUserData({ 
          is_online: true, 
          last_sync: new Date().toISOString()
        });
        
        console.log("Welcome back!");
        window.location.href = '/dashboard';
      } else {
        const approvedNotifications = await NotificationService.filter({
          recipient_email: currentUser.email,
          type: 'join_approved',
          room_code: roomCode.trim()
        });

        if (approvedNotifications.length > 0) {
          sessionStorage.setItem('roomCode', roomCode.trim());
          window.location.href = '/setup';
        } else {
          setShowJoinDialog(true);
        }
      }
    } catch {
      setError("Unable to access your space. Please ensure you are logged in and check your credentials.");
    }
    
    setIsLoading(false);
  };

  const handleJoinRequest = async () => {
    if (!joinRequestName.trim()) {
      console.error("Please enter your name.");
      return;
    }

    setShowJoinDialog(false);
    setIsLoading(true);

    try {
      const currentUser = await UserService.me();
      
      const existingRequests = await NotificationService.filter({
        recipient_email: roomCreator.email,
        type: 'join_request',
        related_id: currentUser.email,
        room_code: roomCode,
        is_read: false
      });

      if (existingRequests.length > 0) {
        console.error("You already have a pending request for this room. Please wait for approval.");
        setIsLoading(false);
        return;
      }
      
      await NotificationService.create({
        recipient_email: roomCreator.email,
        type: 'join_request',
        title: 'New Join Request',
        message: `${joinRequestName} (${currentUser.email}) wants to join your room "${roomCode}" with PIN ${pin}. Do you want to approve this request?`,
        room_code: roomCode,
        related_id: currentUser.email,
        related_type: 'join_request',
        action_url: '/profile'
      });

      setPin('');
      setJoinRequestName('');
      setStep('pending');
      console.log("Join request sent! Please wait for approval.");
    } catch (error) {
      console.error('Error sending join request:', error);
    }
    
    setIsLoading(false);
  };

  const goToSetup = () => {
    sessionStorage.setItem('roomCode', roomCode.trim());
    window.location.href = '/setup';
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-purple-50 via-pink-50 to-rose-100 flex flex-col relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-pink-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
      </div>

      <header className="p-6 relative z-10">
        <Link href="/" className="flex items-center gap-2 text-romantic-600 hover:text-romantic-700 transition-all duration-300 group">
          <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
          <span className="font-semibold">Back to Home</span>
        </Link>
      </header>

      <div className="flex-1 flex items-center justify-center p-4 relative z-10">
        <div className="w-full max-w-lg space-y-8 animate-fade-in-scale">
          <div className="text-center">
            <div className="inline-block p-6 bg-white/80 backdrop-blur-sm rounded-3xl shadow-romantic mb-6 animate-pulse-romantic">
              <Heart className="w-16 h-16 text-romantic-500" />
            </div>
            <h1 className="text-5xl md:text-6xl font-display font-bold gradient-text mb-4">
              Between Us
            </h1>
            <p className="text-xl text-gray-700 font-medium">
              Enter your private space
            </p>
          </div>

          <Card className="bg-white/70 backdrop-blur-lg border-0 shadow-xl">
            {step === 'room' && (
              <>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl text-purple-900">Room Code</CardTitle>
                  <CardDescription>
                    Enter your shared room code to access your private space.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleRoomVerification} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="room-code" className="text-sm font-medium text-purple-800">Shared Room Code</Label>
                      <Input
                        id="room-code"
                        type="text"
                        placeholder="Enter your room code"
                        value={roomCode}
                        onChange={(e) => setRoomCode(e.target.value)}
                        className="h-12 text-lg"
                        autoComplete="off"
                      />
                    </div>

                    {error && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600">{error}</p>
                      </div>
                    )}

                    <Button
                      type="submit"
                      disabled={isLoading || !roomCode.trim()}
                      className="w-full h-12 text-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg transition-shadow"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          Verifying...
                        </div>
                      ) : (
                        <>
                          Verify Room Code <ArrowRight className="w-5 h-5 ml-2" />
                        </>
                      )}
                    </Button>
                  </form>
                  
                  <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900 mb-2">First Time Here?</h4>
                    <p className="text-sm text-purple-700">
                      Create a unique room code (like &ldquo;ourspace2024&rdquo;). If the room doesn&apos;t exist, you&apos;ll be guided to set it up.
                    </p>
                  </div>
                </CardContent>
              </>
            )}

            {step === 'pin' && (
              <>
                <CardHeader className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-sm text-green-600">Room found!</span>
                  </div>
                  <CardTitle className="text-2xl text-purple-900">Enter Your Pin</CardTitle>
                  <CardDescription>
                    Enter your personal 8-digit pin for room: <strong>{roomCode}</strong>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handlePinLogin} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="pin" className="text-sm font-medium text-purple-800">Your 8-Digit Pin</Label>
                      <div className="relative">
                        <Input
                          id="pin"
                          type={showPin ? "text" : "password"}
                          placeholder="Enter your pin"
                          value={pin}
                          onChange={(e) => setPin(e.target.value.slice(0, 8))}
                          className="h-12 text-lg pr-12"
                          maxLength={8}
                          autoComplete="off"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPin(!showPin)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-500 hover:text-purple-700"
                        >
                          {showPin ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                        </button>
                      </div>
                    </div>

                    {error && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600">{error}</p>
                      </div>
                    )}

                    <div className="flex gap-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setStep('room')}
                        className="flex-1"
                      >
                        Back
                      </Button>
                      <Button
                        type="submit"
                        disabled={isLoading || pin.length !== 8}
                        className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                      >
                        {isLoading ? (
                          <div className="flex items-center justify-center gap-2">
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                            Signing In...
                          </div>
                        ) : (
                          <>
                            Sign In <ArrowRight className="w-4 h-4 ml-2" />
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </>
            )}

            {step === 'pending' && (
              <>
                <CardHeader className="text-center">
                  <Clock className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
                  <CardTitle className="text-2xl text-purple-900">Request Pending</CardTitle>
                  <CardDescription>
                    Your join request has been sent to the room creator.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center space-y-4">
                    <p className="text-purple-700">
                      You&apos;ll receive a notification once your request is approved.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => setStep('room')}
                      className="w-full"
                    >
                      Try Different Room
                    </Button>
                  </div>
                </CardContent>
              </>
            )}

            {step === 'setup' && (
              <>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl text-purple-900">New Room!</CardTitle>
                  <CardDescription>
                    Room &ldquo;<strong>{roomCode}</strong>&rdquo; doesn&apos;t exist. Let&apos;s create it for you.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">What happens next?</h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• You&apos;ll set up your profile and choose your pin</li>
                        <li>• Share the room code &ldquo;{roomCode}&rdquo; with your partner</li>
                        <li>• They can join using the same room code with their own pin</li>
                      </ul>
                    </div>
                    
                    <Button
                      onClick={goToSetup}
                      className="w-full h-12 text-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                    >
                      Continue to Setup <ArrowRight className="w-5 h-5 ml-2" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      onClick={() => setStep('room')}
                      className="w-full"
                    >
                      Back to Room Code
                    </Button>
                  </div>
                </CardContent>
              </>
            )}
          </Card>
        </div>
      </div>

      <Dialog open={showJoinDialog} onOpenChange={setShowJoinDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Join Existing Room</DialogTitle>
            <DialogDescription>
              This PIN is not recognized for room &ldquo;{roomCode}&rdquo;. Would you like to request to join this room?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div>
              <Label htmlFor="join-name" className="text-sm font-medium">Your Name</Label>
              <Input
                id="join-name"
                value={joinRequestName}
                onChange={(e) => setJoinRequestName(e.target.value)}
                placeholder="Enter your full name"
                className="mt-1"
              />
            </div>
            <p className="text-sm text-gray-600">
              A notification will be sent to the room creator for approval. You&apos;ll be able to complete your profile once approved.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowJoinDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleJoinRequest} 
              className="bg-gradient-to-r from-purple-500 to-pink-500"
              disabled={!joinRequestName.trim()}
            >
              Send Join Request
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}