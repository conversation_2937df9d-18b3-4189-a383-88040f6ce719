"use client";
import React, { useState, useEffect } from "react";
import { JournalEntry, RomanticPlan, User, JournalEntryService, RomanticPlanService, UserService } from "@/src/lib/entities/all";
import MoodHeatmap from "@/src/components/dashboard/MoodHeatmap";
import QuickStats from "@/src/components/dashboard/QuickStats"; 
import QuickActions from "@/src/components/dashboard/QuickActions";
import RecentActivity from "@/src/components/dashboard/RecentActivity";

export default function Dashboard() {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [romanticPlans, setRomanticPlans] = useState<RomanticPlan[]>([]);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const currentUser = await UserService.me();
      setUser(currentUser);
      const roomCode = sessionStorage.getItem('roomCode');

      if (roomCode) {
        const [entriesData, plansData] = await Promise.all([
          JournalEntryService.filter({ room_code: roomCode, is_draft: false, is_secret: false }),
          RomanticPlanService.filter({ room_code: roomCode })
        ]);
        setEntries(entriesData);
        setRomanticPlans(plansData);
      }
    } catch (error) {
      console.error('Error in loadData structure:', error);
    }
    setIsLoading(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse mx-auto mb-4"></div>
          <p className="text-purple-600 font-medium">Loading your connection...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 md:p-8 bg-gradient-to-br from-purple-50/30 via-pink-50/30 to-rose-50/30 relative">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tr from-pink-200/20 to-purple-200/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="container-romantic space-y-10 relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-12 animate-fade-in-up">
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-romantic rounded-2xl flex items-center justify-center shadow-romantic animate-pulse-romantic">
              <Heart className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-display font-bold gradient-text">
              Welcome Back, {user?.full_name?.split(' ')?.[0] || 'Friend'}
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Your emotional journey continues. Track your connection, celebrate your love, and grow together in this beautiful space you've created.
          </p>
        </div>

        {/* Enhanced Quick Stats */}
        <div className="animate-fade-in-up" style={{animationDelay: '0.2s'}}>
          <QuickStats entries={entries} romanticPlans={romanticPlans} currentUser={user} />
        </div>

        {/* Enhanced Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8 animate-fade-in-up" style={{animationDelay: '0.4s'}}>
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            <MoodHeatmap entries={entries} />
            <RecentActivity entries={entries} currentUser={user} />
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            <QuickActions />

            {/* Enhanced Connection Insight */}
            <div className="card-romantic p-8 text-center hover-lift group">
              <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-emerald-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-romantic group-hover:shadow-glow transition-all duration-300 group-hover:scale-110">
                <span className="text-3xl">💚</span>
              </div>
              <h3 className="font-display font-bold text-xl text-gray-800 mb-3">Strong Connection</h3>
              <p className="text-gray-600 leading-relaxed">
                You&apos;re both actively working on your relationship. Keep up the beautiful work and continue nurturing your bond!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
