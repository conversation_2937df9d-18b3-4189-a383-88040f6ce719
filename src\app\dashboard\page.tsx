"use client";
import React, { useState, useEffect } from "react";
import { JournalEntry, RomanticPlan, User, JournalEntryService, RomanticPlanService, UserService } from "@/src/lib/entities/all";
import MoodHeatmap from "@/src/components/dashboard/MoodHeatmap";
import QuickStats from "@/src/components/dashboard/QuickStats"; 
import QuickActions from "@/src/components/dashboard/QuickActions";
import RecentActivity from "@/src/components/dashboard/RecentActivity";

export default function Dashboard() {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [romanticPlans, setRomanticPlans] = useState<RomanticPlan[]>([]);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const currentUser = await UserService.me();
      setUser(currentUser);
      const roomCode = sessionStorage.getItem('roomCode');

      if (roomCode) {
        const [entriesData, plansData] = await Promise.all([
          JournalEntryService.filter({ room_code: roomCode, is_draft: false, is_secret: false }),
          RomanticPlanService.filter({ room_code: roomCode })
        ]);
        setEntries(entriesData);
        setRomanticPlans(plansData);
      }
    } catch (error) {
      console.error('Error in loadData structure:', error);
    }
    setIsLoading(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse mx-auto mb-4"></div>
          <p className="text-purple-600 font-medium">Loading your connection...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 md:p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold gradient-text mb-3">Welcome Back, {user?.full_name?.split(' ')?.[0] || 'Friend'}</h1>          <p className="text-lg text-purple-600 max-w-2xl mx-auto">
            Your emotional journey continues. Track your connection, celebrate your love, and grow together.
          </p>
        </div>

        {/* Quick Stats */}
        <QuickStats entries={entries} romanticPlans={romanticPlans} currentUser={user} />

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            <MoodHeatmap entries={entries} />
            <RecentActivity entries={entries} currentUser={user} />
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <QuickActions />
            
            {/* Connection Insight */}
            <div className="card-premium p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💚</span>
              </div>
              <h3 className="font-semibold text-purple-900 mb-2">Strong Connection</h3>
              <p className="text-sm text-purple-600">
                You&apos;re both actively working on your relationship. Keep up the beautiful work!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
