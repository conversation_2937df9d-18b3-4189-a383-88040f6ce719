import { JournalEntryService, RomanticPlanService, NotificationService, UserService } from '../entities/all';

/**
 * Service to handle real-time synchronization between partners
 */
export class SyncService {
  private static SYNC_INTERVAL = 10000; // 10 seconds
  private static syncIntervalId: NodeJS.Timeout | null = null;
  private static lastSyncTime: string | null = null;
  private static callbacks: {
    onPartnerStatusChange?: (isOnline: boolean, lastSync: string | null) => void;
    onDataChange?: (changes: unknown) => void;
    onNewNotification?: (notification: unknown) => void;
  } = {};

  /**
   * Start the synchronization process
   */
  static startSync(callbacks: {
    onPartnerStatusChange?: (isOnline: boolean, lastSync: string | null) => void;
    onDataChange?: (changes: unknown) => void;
    onNewNotification?: (notification: unknown) => void;
  }) {
    this.callbacks = callbacks;
    this.updateUserStatus(true);
    
    // Clear any existing interval
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
    }
    
    // Start polling
    this.syncIntervalId = setInterval(() => {
      this.performSync();
    }, this.SYNC_INTERVAL);
    
    // Perform initial sync
    this.performSync();
    
    // Add event listeners for tab visibility
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  }
  
  /**
   * Stop the synchronization process
   */
  static stopSync() {
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
      this.syncIntervalId = null;
    }
    
    this.updateUserStatus(false);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
  }
  
  /**
   * Handle visibility change events
   */
  private static handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      this.updateUserStatus(true);
      this.performSync(); // Sync immediately when tab becomes visible
    } else {
      this.updateUserStatus(false);
    }
  };
  
  /**
   * Handle before unload events
   */
  private static handleBeforeUnload = () => {
    this.updateUserStatus(false);
  };
  
  /**
   * Update the user's online status
   */
  private static async updateUserStatus(isOnline: boolean) {
    try {
      await UserService.updateMyUserData({
        is_online: isOnline,
        last_sync: new Date().toISOString(),
      });
    } catch (error) {
      console.warn("Could not update user status:", error);
    }
  }
  
  /**
   * Perform the synchronization
   */
  private static async performSync() {
    try {
      const roomCode = sessionStorage.getItem('roomCode');
      if (!roomCode) return;
      
      const currentUser = await UserService.me();
      
      // Check partner status
      const users = await UserService.filter({ room_code: roomCode });
      const partner = users.find(u => u.id !== currentUser.id);
      
      if (partner && this.callbacks.onPartnerStatusChange) {
        this.callbacks.onPartnerStatusChange(
          partner.is_online || false, 
          partner.last_sync
        );
      }
      
      // Check for new notifications
      const notifications = await NotificationService.filter({
        recipient_email: currentUser.email,
        room_code: roomCode,
        is_read: false
      });
      
      if (notifications.length > 0 && this.callbacks.onNewNotification) {
        this.callbacks.onNewNotification(notifications);
      }
      
      // Check for data changes since last sync
      if (this.lastSyncTime) {
        const lastSync = new Date(this.lastSyncTime);
        
        // Get journal entries updated since last sync
        const newEntries = await JournalEntryService.filter({ 
          room_code: roomCode,
          updated_after: lastSync.toISOString()
        });
        
        // Get romantic plans updated since last sync
        const newPlans = await RomanticPlanService.filter({
          room_code: roomCode,
          updated_after: lastSync.toISOString()
        });
        
        if ((newEntries.length > 0 || newPlans.length > 0) && this.callbacks.onDataChange) {
          this.callbacks.onDataChange({
            journalEntries: newEntries,
            romanticPlans: newPlans
          });
        }
      }
      
      // Update last sync time
      this.lastSyncTime = new Date().toISOString();
    } catch (error) {
      console.warn("Error during sync:", error);
    }
  }
}