export interface JournalEntry {
  title?: string;
  content: string;
  entry_type: 'minor_issue' | 'habitual' | 'emotional_hurt' | 'needs_discussion' | 'praise' | 'gratitude' | 'romantic_idea';
  mood: 'very_happy' | 'happy' | 'neutral' | 'sad' | 'frustrated' | 'angry';
  is_anonymous?: boolean;
  is_vault?: boolean;
  is_secret?: boolean;
  is_draft?: boolean;
  reveal_date?: string;
  tags?: string[];
  sentiment_score?: number;
  is_resolved?: boolean;
  partner_response?: string;
}